/* Import base styles and variables */
@import 'base.css';

/* Import component styles */
@import 'components/card.css';
@import 'components/about.css';
@import 'components/services.css';
@import 'components/service-area.css';
@import 'components/notification.css';

/* Import effect styles */
@import 'effects/holographic.css';
@import 'effects/shine.css';
@import 'effects/animations.css';

/* Import responsive styles (consolidated media queries) */
@import 'responsive.css';

/* Disable main page scrollbar */
html, body {
    margin: 0;
    padding: 0;
    overflow: hidden;
    height: 100%;
    width: 100%;
}

.card-container {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}


