# Build System Documentation

## Overview
This project uses a simple Node.js build system to prepare files for web hosting deployment.

## Quick Start

### Build for Production
```bash
npm run build
```

This command will:
1. Clean the `build/` directory
2. Copy all necessary files (HTML, CSS, JS, assets)
3. Update sitemap.xml with current dates
4. Prepare everything for upload to your web host

### Manual Clean
```bash
npm run build:clean
```

## What Gets Built

### Copied Directories:
- `scripts/` - All JavaScript files
- `assets/` - Images, fonts, and other assets  
- `styles/` - All CSS files

### Copied Files:
- `index.html`
- `booking.html`
- `booking-form.html`
- `.htaccess`
- `sitemap.xml` (with updated dates)
- `robots.txt`

### Excluded Files:
- Development files (package.json, server.js, build.js)
- Source maps (*.map)
- Backup files (*.old, *.backup)
- Log files (*.log)
- Temporary files (*.tmp)

## Sitemap Updates

The build process automatically updates the `<lastmod>` dates in sitemap.xml to the current date in ISO format (YYYY-MM-DD).

URLs that get updated:
- `https://ocalamobilemassage.com/`
- `https://ocalamobilemassage.com/index.html`
- `https://ocalamobilemassage.com/booking-form.html`
- `https://ocalamobilemassage.com/booking.html`

## Deployment

After running `npm run build`:

1. Navigate to the `build/` folder
2. Upload all contents to your web host
3. Your site is ready!

## Configuration

Edit `build-config.js` to:
- Add/remove files or directories to copy
- Modify sitemap URL list
- Change exclude patterns
- Adjust build output directory

## File Structure After Build

```
build/
├── assets/
├── scripts/
├── styles/
├── index.html
├── booking.html
├── booking-form.html
├── .htaccess
├── sitemap.xml (updated)
└── robots.txt
```
