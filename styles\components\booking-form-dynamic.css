/**
 * Booking Form Dynamic Styles
 * 
 * This file contains CSS classes to replace all inline styles in booking-form.js
 * Created for Content Security Policy compliance - eliminates need for 'unsafe-inline'
 */

/*===========================================*/
/*           ANIMATION CONTROL CLASSES       */
/*===========================================*/

/* Animation Reset Class */
.reset-animation {
    animation: none !important;
}



/* Animation Order CSS Custom Properties */
.animation-order-0 { --animation-order: 0; }
.animation-order-1 { --animation-order: 1; }
.animation-order-2 { --animation-order: 2; }
.animation-order-3 { --animation-order: 3; }
.animation-order-4 { --animation-order: 4; }
.animation-order-5 { --animation-order: 5; }
.animation-order-6 { --animation-order: 6; }
.animation-order-7 { --animation-order: 7; }
.animation-order-8 { --animation-order: 8; }
.animation-order-9 { --animation-order: 9; }
.animation-order-10 { --animation-order: 10; }

/*===========================================*/
/*           DISPLAY CONTROL CLASSES         */
/*===========================================*/

/* Force Block Display */
.force-block {
    display: block !important;
}

/* Force Hidden Display */
.force-hidden {
    display: none !important;
}



/*===========================================*/
/*           READY MESSAGE CLASSES           */
/*===========================================*/

/* Ready Message Enhanced Styling */
.ready-message-enhanced {
    padding: 1rem;
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 2px 8px rgba(79, 209, 197, 0.15);
    margin-bottom: 1.5rem;
}
