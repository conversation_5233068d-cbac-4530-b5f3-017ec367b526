
/*===========================================*/
/*              GLOBAL STYLES                */
/*===========================================*/

/* Global Box Model */
*, *::before, *::after {
    box-sizing: border-box;
}

/* Global interactive element styles */
button, [role="button"], .clickable, a {
    -webkit-tap-highlight-color: transparent;
}

/* Accessibility Focus Styles */
button:focus, [role="button"]:focus, .clickable:focus, a:focus,
.service-option:focus, .duration-option:focus {
    outline: 2px solid hsl(184, 70%, 35%);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(79, 209, 197, 0.3);
}

/* Focus styles for keyboard navigation only */
button:focus:not(:focus-visible),
[role="button"]:focus:not(:focus-visible),
.clickable:focus:not(:focus-visible),
a:focus:not(:focus-visible),
.service-option:focus:not(:focus-visible),
.duration-option:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
}

button:focus-visible,
[role="button"]:focus-visible,
.clickable:focus-visible,
a:focus-visible,
.service-option:focus-visible,
.duration-option:focus-visible {
    outline: 2px solid hsl(184, 70%, 35%);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(79, 209, 197, 0.3);
}

/*===========================================*/
/*              UTILITY CLASSES              */
/*===========================================*/

/* Typography Utilities */
.text-center { text-align: center; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.opacity-90 { opacity: 0.9; }
.text-white { color: white; }

/* Layout Utilities */
.flex { display: flex; }
.grid { display: grid; }
.flex-wrap { flex-wrap: wrap; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }

/* Container Utilities */
.w-full { width: 100%; }
.hidden { display: none; }

/* Spacing Utilities - Margins */
.mt-1 { margin-top: 0.25rem; }
.mt-3 { margin-top: 0.75rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mr-1 { margin-right: 0.25rem; }
.ml-1 { margin-left: 0.25rem; }

/* Spacing Utilities - Paddings */
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

/* Border Utilities */
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-4xl { border-radius: 2rem; }
.rounded-full { border-radius: 9999px; }

/* Effect Utilities */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1); }

/* Transition Utilities */
.transition { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }

/* Button State Utilities */
.disabled\\:opacity-50:disabled { opacity: 0.5; }
.disabled\\:cursor-not-allowed:disabled { cursor: not-allowed; }

/*===========================================*/
/*           RESPONSIVE UTILITIES           */
/*===========================================*/

/* Breakpoint Variables (for reference in comments) */
/* --mobile-max: 640px; */
/* --tablet-min: 641px; */
/* --tablet-max: 1024px; */
/* --desktop-min: 1025px; */

/* Mobile First Responsive Utilities */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.flex-col { flex-direction: column; }

/* Tablet (641px and up) */
@media (min-width: 641px) {
    .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\\:flex-row { flex-direction: row; }
    .md\\:text-left { text-align: left; }
}

/* Desktop (1025px and up) */
@media (min-width: 1025px) {
    .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/*===========================================*/
/*           SERVICE OPTION STYLES           */
/*===========================================*/

/* Service Options Container */
#service-options {
    width: 100%;
    box-sizing: border-box;
}

/* Duration Options Container */
#duration-options {
    width: 100%;
    box-sizing: border-box;
}

/* Service Text Styles */
.service-selector-h3 {
    color: #555;
    font-family: 'Poppins', sans-serif;
    font-size: 1.125rem;
    margin-bottom: 0.15rem; /* Reduced from 0.25rem to move description up */
    line-height: 1.3; /* Slightly reduced line height */
}

.service-selector-p {
    color: #666;
    font-family: 'Lato', sans-serif;
    line-height: 1.3; /* Reduced from 1.4 to make text more compact */
    margin-top: -2px; /* Negative margin to pull text up closer to the title */
}

.service-text-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 2px; /* Slight padding to better align with icon */
}

/* Mobile Service Text Styles will be consolidated in the MOBILE RESPONSIVE STYLES section */

/*===========================================*/
/*               BODY STYLES                */
/*===========================================*/

body {
    font-family: 'Lato', sans-serif;
    background: linear-gradient(to top left, #4fd1c5, #e6fffa);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 1rem;
    margin: 0;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/*===========================================*/
/*           CALCULATOR CARD STYLES          */
/*===========================================*/

/* Main Card Container */
.calculator-card {
    background-color: #eee;
    border-radius: 1rem;
    box-shadow: 0 35px 50px -12px rgba(0, 0, 0, 0.25);
    max-width: 600px;
    width: 100%;
    position: relative;
    transition: transform 0.05s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease;
    border: 1px solid #aaa;
    overflow: hidden;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

.calculator-card:hover {
    box-shadow: 0 0 25px 8px rgba(255, 255, 255, 0.7);
}

/* Card Header */
.card-header {
    width: 100%;
    background-color: hsl(184, 70%, 40%);
    color: white;
    padding: 1.5rem;
    text-align: center;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
    position: relative;
    z-index: 2;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

/* Card Body */
.card-body {
    padding: 1.5rem;
    background-color: white;
    position: relative;
    z-index: 2;
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

/*===========================================*/
/*          SERVICE OPTION STYLES            */
/*===========================================*/

/* Service Option Container */
.service-option {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 16px 20px; /* Reduced vertical padding to make layout more compact */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    width: 100%; /* Ensure full width in grid cell */
    box-sizing: border-box; /* Ensure padding is included in width calculation */
    -webkit-tap-highlight-color: transparent; /* Prevent blue flash on mobile tap */
    outline: none; /* Remove outline on click/focus */
    user-select: none; /* Prevent text selection */
    display: none;
    align-items: center;
    justify-content: space-between;
}

/* Service Option Animation */
#service-section:not(.hidden-section) .service-option,
#person-section:not(.hidden-section) .service-option {
    display: flex;
    opacity: 0;
    transform: translateY(10px);
    animation: fadeInUp 0.5s cubic-bezier(0.25, 0.1, 0.25, 1.0) forwards;
    animation-delay: calc(var(--animation-order, 0) * 0.08s + 0.1s); /* Faster staggered effect with a base delay */
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Service Option States */
.service-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: rgba(79, 209, 197, 0.3);
    background-color: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
}

.service-option.selected {
    color: white;
    border-color: hsl(184, 70%, 40%);
    background-color: hsl(184, 70%, 40%);
    transform: translateY(-2px);
}

/* Service Icon Styles */
.service-selector-icon {
    font-size: 1.8rem; /* Much larger icon */
    color: hsl(184, 70%, 35%);
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px; /* Fixed width for alignment */
    height: 60px; /* Fixed height for alignment */
    flex-shrink: 0; /* Prevent icon from shrinking */
    background-color: rgba(79, 209, 197, 0.3);
    border-radius: 100%;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    border: 2px solid transparent;
    opacity: 0;
    animation: icon-intro 1.5s forwards alternate;
    transition: all 0.2s ease;
}

/* Icon Animations */
@keyframes icon-intro {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes icon-bounce {
    0% {
        transform: scale(0.5);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes icon-hover {
    0% {
        transform: scale(0.8);
    }
    100% {
        transform: scale(1.1);
    }
}

/* Icon State Styles */
.service-option:hover .service-selector-icon {
    opacity: 1;
    animation: icon-hover 0.5s forwards alternate !important;
}

.service-option.selected .service-selector-icon {
    opacity: 1;
    animation: icon-bounce 0.5s forwards alternate !important;
    box-shadow: inset 0 0 6px 2px rgba(79, 209, 197, 0.5);
    color: #ffffff;
    transition: color 0.2s ease;
}

/* Selected Service Text Styles */
.service-option.selected .service-selector-h3 {
    text-shadow: 0 0 10px rgba(79, 209, 197, 0.5);
    color: #ffffff;
    transition: color 0.2s ease;
}

.service-option.selected .service-selector-p {
    text-shadow: 0 0 10px rgba(79, 209, 197, 0.5);
    color: #EEEEEE;
    transition: color 0.2s ease;
}

/*===========================================*/
/*      CORPORATE SESSIONS INTERFACE        */
/*===========================================*/

/* Corporate Sessions Interface Container */
.corporate-sessions-interface {
    width: 100%;
    box-sizing: border-box;
}

/* Corporate Sessions Card */
.corporate-sessions-card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    border: 2px solid rgba(79, 209, 197, 0.2);
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

/* Corporate Sessions Header */
.corporate-sessions-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.sessions-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(184, 70%, 35%);
    font-family: 'Poppins', sans-serif;
    margin-bottom: 0.5rem;
}

.sessions-subtitle {
    font-size: 1rem;
    color: #666;
    font-family: 'Lato', sans-serif;
    margin: 0;
}

/* Corporate Sessions Controls */
.corporate-sessions-controls {
    margin-bottom: 1.5rem;
}

.sessions-counter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.sessions-label {
    font-size: 1rem;
    font-weight: 600;
    color: hsl(184, 70%, 35%);
    font-family: 'Lato', sans-serif;
}

.sessions-counter-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    background-color: rgba(79, 209, 197, 0.1);
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
}

.counter-button {
    background-color: hsl(184, 70%, 40%);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

.counter-button:hover:not(:disabled) {
    background-color: hsl(184, 70%, 35%);
    transform: scale(1.05);
}

.counter-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

.sessions-display {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(184, 70%, 35%);
    font-family: 'Poppins', sans-serif;
    min-width: 120px;
    text-align: center;
}

/* Corporate Sessions Summary */
.corporate-sessions-summary {
    background-color: rgba(79, 209, 197, 0.1);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.summary-label {
    font-size: 1rem;
    font-weight: 500;
    color: #666;
    font-family: 'Lato', sans-serif;
}

.summary-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: hsl(184, 70%, 35%);
    font-family: 'Poppins', sans-serif;
}

/* Corporate Sessions Summary Layout */
#unified-selection-summary:not(.hidden) {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/*===========================================*/
/*         GROUP SIZE COUNTER STYLES        */
/*===========================================*/

/* Group Size Counter Container */
.group-size-counter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    border: 2px solid rgba(79, 209, 197, 0.2);
    transition: all 0.2s ease;
}

/* Counter Display */
.counter-display {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 1rem;
    background-color: rgba(79, 209, 197, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(79, 209, 197, 0.3);
}



/* Counter Value Display */
.counter-value {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    min-width: 80px;
}

.people-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: hsl(184, 70%, 35%);
    font-family: 'Poppins', sans-serif;
    line-height: 1;
}

.people-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #666;
    font-family: 'Lato', sans-serif;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Counter Info */
.counter-info {
    text-align: center;
}

/* Group Size Notification */
.group-size-notification {
    font-weight: 500;
    line-height: 1.4;
}

.group-size-notification strong {
    font-weight: 600;
}

/*===========================================*/
/*         DURATION OPTION STYLES           */
/*===========================================*/

/* Duration Option Container */
.duration-option {
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 10px 20px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(79, 209, 197, 0.2);
    display: inline-block; /* Ensure proper inline layout */
    margin-right: 0.5rem; /* Add spacing between options */
    margin-bottom: 0.5rem; /* Add spacing for wrapping */
    box-sizing: border-box; /* Ensure padding is included in width calculation */
    -webkit-tap-highlight-color: transparent; /* Prevent blue flash on mobile tap */
    outline: none; /* Remove outline on click/focus */
    user-select: none; /* Prevent text selection */
}

/* Duration Option States */
.duration-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: rgba(79, 209, 197, 0.5);
}

.duration-option.selected {
    transform: translateY(-2px);
    background-color: hsl(184, 70%, 40%);
    color: white;
}

/* Duration Text Styles */
.duration-selector-h3 {
    color: #555;
}

.duration-selector-p {
    color: hsl(184, 70%, 35%);
}

/* Selected Duration Text Styles */
.duration-option.selected .duration-selector-h3,
.duration-option.selected .duration-selector-p {
    color: white;
}

/*===========================================*/
/*         RIVE ANIMATION CONTAINER          */
/*===========================================*/

/* Main Animation Container */
#animation-container {
    width: 100%;
    min-height: 240px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
    background-color: #f8fafc;
    border-radius: 1rem;
    padding: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(79, 209, 197, 0.2);
    gap: 2.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    box-sizing: border-box; /* Ensure padding is included in width calculation */
}

/* Canvas Element */
#canvas {
    width: 160px;
    height: 160px;
    flex-shrink: 0;
}

/* Rive Canvas Container */
.rive-canvas-container {
    position: absolute;
    width: 160px;
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 999px;
    box-shadow: 0 0px 4px 2px rgb(255, 255, 255);
    transition: all 0.0s cubic-bezier(0.25, 0.1, 0.25, 1.0); /* Improved easing for smoother motion */
}

/* Centered Canvas Container */
.rive-canvas-container.centered {
    position: absolute;
    left: 50%;
    transform: translateX(-50%); /* Centered position */
}

/* Intro Animation Active State */
.intro-animation-active {
    min-height: 200px !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Relaxation Level Text Container */
.current-relaxation-level {
    transition: opacity 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0), transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

/* Hidden Text State */
.current-relaxation-level.hidden-text {
    opacity: 0;
    transform: translateX(30px);
    pointer-events: none;
    display: none; /* Completely hide it initially */
}

/*===========================================*/
/*         MOBILE RESPONSIVE STYLES          */
/*===========================================*/

/* Mobile Styles (max-width: 640px) */
@media (max-width: 640px) {
    /* ---- Animation and Relaxation Level ---- */

    /* Mobile Animation Container */
    #animation-container {
        flex-direction: column !important; /* Force column layout on mobile */
        padding: 1rem;
        margin-bottom: 1rem;
        gap: 0rem; /* Reset gap for mobile layout */
        min-height: 320px; /* Ensure enough space for both elements */
    }

    /* Mobile Canvas Element */
    #canvas {
        margin-bottom: 0.5rem;
    }

    /* Mobile Relaxation Level Text */
    .current-relaxation-level {
        text-align: center;
        padding: 0.75rem;
        align-items: center;
        justify-content: center; /* Center content vertically */
        max-width: 100%;
        width: 100%; /* Full width on mobile */
        min-width: unset; /* Override desktop min-width */
        margin-top: 0.5rem; /* Add space between canvas and text */
    }

    /* Mobile Relaxation Title */
    .relaxation-title {
        text-align: center;
        font-size: 1.35rem;
        margin-bottom: 0.75rem;
        width: 100%; /* Ensure full width for proper centering */
    }

    /* Mobile Relaxation Value */
    .relaxation-value {
        text-align: center;
        font-size: 2rem;
        width: 100%; /* Ensure full width for proper centering */
    }

    /* Mobile Canvas Container Positioning */
    #animation-container > div:first-child {
        margin: 0 auto;
        display: flex;
        justify-content: center;
        position: relative !important; /* Force relative positioning */
        left: auto !important; /* Reset any absolute positioning */
        transform: none !important; /* Reset any transforms */
    }

    /* Mobile Centered Canvas Container */
    .rive-canvas-container.centered {
        position: relative !important;
        left: auto !important;
        transform: none !important;
        margin: 0 auto;
    }

    /* Mobile Canvas Container */
    .rive-canvas-container {
        margin: 0 auto;
        position: relative !important;
        width: 160px !important;
        height: 160px !important;
    }

    /* Mobile Hidden Text State */
    .current-relaxation-level.hidden-text {
        opacity: 0;
        transform: none;
        display: none; /* Completely hide it on mobile */
    }

    /* Mobile Intro Animation Active State */
    .intro-animation-active {
        min-height: 200px !important;
        flex-direction: column !important;
    }

    /* ---- Service Options ---- */

    /* Mobile Service Text Styles */
    .service-selector-h3 {
        font-size: 1rem;
        margin-bottom: 0.1rem; /* Reduced from 0.1rem to move description up */
        line-height: 1.15; /* Slightly reduced line height */
    }

    .service-selector-p {
        line-height: 1.15; /* Reduced from 1.2 to make text more compact */
        font-size: 0.8rem;
        margin-top: -0.5px; /* Negative margin to pull text up closer to the title */
    }

    .service-text-container {
        min-height: 40px; /* Match the icon height */
    }

    /* Mobile Grid Layout */
    .grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    /* Mobile Service Options */
    .service-option {
        padding: 12px; /* Reduced padding */
        margin-bottom: 8px; /* Reduced margin between options */
    }

    /* Mobile Service Icons */
    .service-selector-icon {
        font-size: 1.125rem; /* Smaller icon */
        width: 40px; /* Smaller width */
        height: 40px; /* Smaller height */
        margin-right: 0.5rem; /* Less spacing */
        display: flex;
        align-items: center;
        justify-content: center;
        align-self: center; /* Ensure vertical centering */
    }

    /* Mobile Service Options Container */
    #service-options {
        gap: 0.5rem; /* Smaller gap */
    }

    /* Mobile Service Section */
    #service-section {
        margin-bottom: 1rem; /* Reduced from 1.5rem */
    }



    /* ---- Corporate Sessions Mobile Styles ---- */

    /* Mobile Corporate Sessions Card */
    .corporate-sessions-card {
        padding: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .corporate-sessions-header {
        margin-bottom: 1.25rem;
    }

    .sessions-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }

    .sessions-subtitle {
        font-size: 0.9rem;
    }

    .sessions-counter {
        gap: 0.75rem;
    }

    .sessions-counter-controls {
        padding: 0.5rem 1rem;
        gap: 0.75rem;
    }

    .counter-button {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }

    .sessions-display {
        font-size: 1.1rem;
        min-width: 100px;
    }

    .corporate-sessions-summary {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .summary-label {
        font-size: 0.9rem;
    }

    .summary-value {
        font-size: 1rem;
    }

    /* Mobile unified selection section spacing */
    .unified-selection-interface {
        padding: 0.5rem;
    }

    /* Mobile section title adjustments */
    #unified-selection-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }



    /* ---- Group Size Counter Mobile Styles ---- */

    /* Mobile Group Size Counter */
    .group-size-counter {
        padding: 1rem;
        gap: 0.75rem;
    }

    .counter-display {
        gap: 1.5rem;
        padding: 0.75rem;
    }

    .counter-button {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
    }

    .people-number {
        font-size: 2rem;
    }

    .people-label {
        font-size: 0.75rem;
    }

    /* ---- Touch Target Optimization ---- */

    /* Ensure all interactive elements have adequate touch targets */
    button,
    [role="button"],
    .clickable,
    .service-option,
    .duration-option,
    .counter-button,
    a {
        min-height: 44px; /* Minimum height for touch targets */
        min-width: 44px; /* Minimum width for touch targets */
    }

    /* Add extra padding to small buttons for better touch targets */
    .edit-button {
        padding: 8px 12px;
    }

    /* ---- Book Now and Ready Message Styles ---- */

    .ready-message-item {
        display: block;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
        text-align: center;
        padding: 0.4rem 0;
        border-bottom: 1px solid rgba(79, 209, 197, 0.15);
    }

    .ready-message-item:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }

    /* No need to hide separator in mobile view anymore since we're using stacked format */

    .ready-message-title {
        margin-bottom: 1rem !important;
        font-size: 1.2rem !important;
        text-align: center;
        border-bottom: 1px solid rgba(79, 209, 197, 0.3);
        padding-bottom: 0.75rem;
    }

    .ready-message-details {
        padding: 0 0.5rem;
    }

    #book-now-container .mb-3.text-xl {
        padding: 1.25rem !important;
        margin-bottom: 1.75rem !important;
    }

    /* Mobile Group Size Notification */
    .group-size-notification {
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
        font-size: 0.85rem !important;
        padding: 0.75rem 1rem !important;
    }

    /* Book Now Button - Larger touch target on mobile */
    .book-now-button {
        padding: 1rem 2rem;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }
}
/*===========================================*/
/*       RELAXATION LEVEL INDICATOR          */
/*===========================================*/

/* Relaxation Level Container */
.current-relaxation-level {
    text-align: left;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 1.25rem;
    border-radius: 1rem;
    height: 100%;
    max-width: 200px;
    min-width: 250px;
}

/* Relaxation Title */
.relaxation-title {
    margin-bottom: 0.75rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: hsl(184, 70%, 35%);
    font-family: 'Poppins', sans-serif;
    line-height: 1.2;
}

/* Relaxation Value */
.relaxation-value {
    font-size: 2.25rem;
    font-weight: 700;
    color: #555;
    font-family: 'Lato', sans-serif;
    line-height: 1.2;
}

/*===========================================*/
/*           TRANSITION EFFECTS              */
/*===========================================*/

/* Global Transition Effects */
.service-option, .duration-option, #person-section, #service-section, #duration-section,
#person-options, #service-options, #duration-options,
#person-summary, #service-summary, #duration-summary,
#book-now-container {
    transition: all 0.35s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    /* Slightly longer duration with improved easing for smoother transitions */
}

/*===========================================*/
/*           BOOK NOW BUTTON STYLES           */
/*===========================================*/

/* Book Now Container */
#book-now-container {
    margin-top: 2rem;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out, background-color 0.6s ease-out;
    padding: 1.5rem;
    border-radius: 1rem;
    overflow: hidden;
}

/* Visible Book Now Container */
#book-now-container.visible {
    opacity: 1;
    transform: translateY(0) scale(1);
    background: linear-gradient(to bottom, rgba(79, 209, 197, 0.15), rgba(79, 209, 197, 0.05));
    box-shadow: 0 10px 25px -5px rgba(79, 209, 197, 0.25), inset 0 1px 2px rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(79, 209, 197, 0.3);
}

/*===========================================*/
/*        SUMMARY ANIMATION STYLES           */
/*===========================================*/

/* Initial collapsed state for the booking ready title */
#booking-ready-title {
    height: 0;
    opacity: 0;
    transform: scale(0.8);
    overflow: hidden;
    margin-bottom: 0;
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Expanded state for the booking ready title */
#booking-ready-title.title-visible {
    height: auto;
    opacity: 1;
    transform: scale(1);
    margin-bottom: 1.5rem;
}

/* Initial state for ready message title */
.ready-message-title {
    opacity: 0;
    transform: scale(0.7);
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Visible state for ready message title */
.ready-message-title.animate-in {
    opacity: 1;
    transform: scale(1);
}

/* Initial state for ready message details container */
.ready-message-details {
    height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.4s ease-out;
}

/* Expanded state for ready message details container */
.ready-message-details.details-visible {
    height: auto;
    opacity: 1;
}

/* Initial state for individual ready message items */
.ready-message-item {
    opacity: 0;
    transform: translateY(10px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Visible state for individual ready message items */
.ready-message-item.item-visible {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Book Now Button - Initial state and styling */
.book-now-button {
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(79, 209, 197, 0.5);
    border-radius: 1rem;
    padding: 0.75rem;
    width: 96%;
    padding-left: 3.5rem;
    padding-right: 3.5rem;
    /* Initial animation state */
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Visible state for book button */
.book-now-button.button-visible {
    opacity: 1;
    transform: scale(1);
}

/* Button Hover State */
.book-now-button.button-visible:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 30px -5px rgba(79, 209, 197, 0.7);
    animation: pulse 2s infinite;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Button Active State */
.book-now-button.button-visible:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Button Shine Effect */
.book-now-button::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to bottom right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(45deg);
    animation: shine 6s infinite;
}

/* Ready Message Styles */
.ready-message-item {
    display: block;
    color: hsl(184, 70%, 35%);
    font-weight: 700;
    margin-bottom: 0.5rem;
    padding: 0.4rem 0;
    border-bottom: 1px solid rgba(79, 209, 197, 0.15);
}

.ready-message-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.ready-message-title {
    border-bottom: 1px solid rgba(79, 209, 197, 0.3);
    padding-bottom: 0.75rem;
}

.ready-message-details {
    text-align: center;
    padding: 0 0.5rem;
}

/* Button Animations */
@keyframes highlight-text {
    0% { text-shadow: 0 1px 1px rgba(255,255,255,0.8); }
    50% { text-shadow: 0 0 8px rgba(79, 209, 197, 0.5); }
    100% { text-shadow: 0 1px 1px rgba(255,255,255,0.8); }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(79, 209, 197, 0.7);
    }
    70% {
        box-shadow: 0 0 0 14px rgba(79, 209, 197, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(79, 209, 197, 0);
    }
}

@keyframes shine {
    0% {
        left: -200%;
        top: -100%;
    }
    100% {
        left: 100%;
        top: 100%;
    }
}

/* Mobile Ready Message Styles - Consolidated in the MOBILE RESPONSIVE STYLES section */

/*===========================================*/
/*       SECTION ANIMATION STYLES            */
/*===========================================*/

/* Section Containers */
#person-section, #service-section, #duration-section, #summary-section {
    width: 100%;
    box-sizing: border-box;
}

/* Person Section Animation */
#person-section {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: opacity 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0), transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    /* Slightly faster with improved easing for smoother animation */
}

/* Service Section Animation */
#service-section {
    opacity: 1;
    transform: translateY(0) scale(1);
    transition: opacity 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0), transform 0.7s cubic-bezier(0.25, 0.1, 0.25, 1.0);
    /* Slightly faster with improved easing for smoother animation */
}

/* Hidden Section */
#person-section.hidden-section,
#service-section.hidden-section {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    pointer-events: none;
    display: none; /* Completely hide it initially */
}

/*===========================================*/
/*             SUMMARY STYLES                */
/*===========================================*/

/* Duration Summary */
#duration-summary {
    position: relative;
    padding-left: 2rem;
}

#duration-summary:before {
    content: '✓';
    position: absolute;
    left: 0.75rem;
    color: hsl(184, 70%, 40%);
    font-weight: bold;
}

/* Service Summary */
#service-summary, #person-summary {
    font-weight: 600;
    font-size: 1.125rem;
    font-family: 'Poppins', sans-serif;
}

#service-summary-text:before, #person-summary-text:before {
    content: '✓';
    margin-right: 0.5rem;
    color: hsl(184, 70%, 40%);
    font-weight: bold;
}
