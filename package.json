{"dependencies": {"@rive-app/canvas-lite": "^2.27.1"}, "name": "mobile-massage-main", "version": "1.0.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "build": "node build.js", "build:clean": "node -e \"require('fs').rmSync('build', {recursive: true, force: true}); console.log('Build directory cleaned')\"", "prebuild": "npm run build:clean"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs"}