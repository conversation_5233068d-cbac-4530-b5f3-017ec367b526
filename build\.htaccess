# Security Headers and Configuration for Mobile Massage Site
# Optimized for Hostinger hosting

# Disable directory listing
Options -Indexes

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # XSS Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy - Strict for privacy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Permissions Policy - Restrict unnecessary features
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), accelerometer=(), gyroscope=(), magnetometer=()"
    
    # Strict Transport Security (HSTS) - Enforce HTTPS
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy - Comprehensive security configuration
    Header always set Content-Security-Policy "default-src 'none'; script-src 'self' 'wasm-unsafe-eval'; style-src 'self' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https://fonts.gstatic.com https://cdnjs.cloudflare.com https://*.tile.openstreetmap.org https://*.giphy.com https://media.giphy.com https://*.gstatic.com blob:; connect-src 'self' https://*.tile.openstreetmap.org https://unpkg.com https://cdn.jsdelivr.net; frame-src https://mobile-massage.setmore.com https://*.setmore.com; frame-ancestors 'self'; worker-src 'self' blob:; object-src 'none'; base-uri 'self'; form-action 'self' https://mobile-massage.setmore.com https://*.setmore.com"
    
    # Remove server signature
    Header always unset X-Powered-By
    Header always unset Server
</IfModule>

# Caching Configuration
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Default: 1 month
    ExpiresDefault "access plus 1 month"
    
    # HTML: no cache (for dynamic content)
    ExpiresByType text/html "access plus 0 seconds"
    
    # CSS and JS: 1 year (with versioning)
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # Images: 1 year
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonts: 1 year
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # WebAssembly and Rive files: 1 year
    ExpiresByType application/wasm "access plus 1 year"
    
    # JSON: 1 month
    ExpiresByType application/json "access plus 1 month"
</IfModule>

# Cache Control Headers (more specific than Expires)
<IfModule mod_headers.c>
    # Static assets with long cache and immutable flag
    <FilesMatch "\.(js|css|png|jpg|jpeg|gif|svg|webp|woff2?|ttf|otf|wasm|riv)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>
    
    # HTML files - no cache for dynamic content
    <FilesMatch "\.(html)$">
        Header set Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
    
    # JSON files - moderate cache
    <FilesMatch "\.(json|geojson)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
</IfModule>

# Security: Block access to sensitive files
<FilesMatch "\.(map|log|bak|backup|old|tmp|temp|env|config|ini|conf)$">
    Require all denied
</FilesMatch>

# Block access to source maps (development files)
<FilesMatch "\.map$">
    Require all denied
</FilesMatch>

# Block access to development and system files
<FilesMatch "\.(htaccess|htpasswd|git|svn|md)$">
    Require all denied
</FilesMatch>

# Allow robots.txt specifically
<FilesMatch "^(?!robots\.txt$).*\.txt$">
    Require all denied
</FilesMatch>

# Block access to Node.js files
<FilesMatch "\.(json)$">
    <RequireAll>
        Require all granted
        Require not expr "%{REQUEST_URI} =~ m#/(package|package-lock|tsconfig|webpack|gulpfile)\.json$#"
    </RequireAll>
</FilesMatch>

# Set default index file
DirectoryIndex index.html

# Force HTTPS - Redirect all HTTP traffic to HTTPS
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# Handle 404 errors gracefully
ErrorDocument 404 /index.html

# Compress text files for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# MIME type configuration
<IfModule mod_mime.c>
    # XML files (including sitemap.xml)
    AddType application/xml .xml
    AddType text/xml .xml

    # Specifically for sitemap.xml
    <Files "sitemap.xml">
        ForceType application/xml
    </Files>

    # WebAssembly
    AddType application/wasm .wasm

    # Rive animation files
    AddType application/octet-stream .riv

    # Web fonts
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType font/ttf .ttf
    AddType font/otf .otf
</IfModule>
