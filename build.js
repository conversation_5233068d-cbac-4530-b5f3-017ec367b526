#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const config = require('./build-config');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m'
};

// Utility function to log with colors
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Get current date in ISO format (YYYY-MM-DD)
function getCurrentDate() {
  return new Date().toISOString().split('T')[0];
}

// Recursively create directory if it doesn't exist
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Recursively remove directory and contents
function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
  }
}

// Check if file matches exclude patterns
function shouldExclude(filePath) {
  return config.excludePatterns.some(pattern => {
    // Simple glob pattern matching for common cases
    if (pattern.includes('**')) {
      const regex = pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*');
      return new RegExp(regex).test(filePath);
    }
    return filePath.includes(pattern.replace(/\*/g, ''));
  });
}

// Copy file with directory creation
function copyFile(src, dest) {
  ensureDir(path.dirname(dest));
  fs.copyFileSync(src, dest);
}

// Recursively copy directory
function copyDirectory(src, dest) {
  ensureDir(dest);
  
  const items = fs.readdirSync(src);
  
  for (const item of items) {
    const srcPath = path.join(src, item);
    const destPath = path.join(dest, item);
    const relativePath = path.relative('.', srcPath);
    
    if (shouldExclude(relativePath)) {
      log(`  Skipping: ${relativePath}`, 'yellow');
      continue;
    }
    
    const stat = fs.statSync(srcPath);
    
    if (stat.isDirectory()) {
      copyDirectory(srcPath, destPath);
    } else {
      copyFile(srcPath, destPath);
      log(`  Copied: ${relativePath}`, 'green');
    }
  }
}

// Update sitemap.xml with current dates
function updateSitemap() {
  const sitemapPath = path.join(config.buildDir, 'sitemap.xml');
  
  if (!fs.existsSync(sitemapPath)) {
    log('Warning: sitemap.xml not found in build directory', 'yellow');
    return;
  }
  
  let sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
  const currentDate = getCurrentDate();
  
  log(`Updating sitemap.xml with date: ${currentDate}`, 'cyan');
  
  // Update lastmod dates for configured URLs
  config.sitemap.updateFiles.forEach(url => {
    const urlRegex = new RegExp(`(<loc>${url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}</loc>\\s*<lastmod>)[^<]+(</lastmod>)`, 'g');
    const matches = sitemapContent.match(urlRegex);
    
    if (matches) {
      sitemapContent = sitemapContent.replace(urlRegex, `$1${currentDate}$2`);
      log(`  Updated: ${url}`, 'green');
    }
  });
  
  fs.writeFileSync(sitemapPath, sitemapContent);
}

// Main build function
function build() {
  log('🚀 Starting build process...', 'bright');
  
  // Clean build directory
  log('\n📁 Cleaning build directory...', 'blue');
  removeDir(config.buildDir);
  ensureDir(config.buildDir);
  log('Build directory cleaned', 'green');
  
  // Copy directories
  log('\n📂 Copying directories...', 'blue');
  config.directories.forEach(dir => {
    if (fs.existsSync(dir)) {
      log(`Copying directory: ${dir}`, 'cyan');
      copyDirectory(dir, path.join(config.buildDir, dir));
    } else {
      log(`Warning: Directory ${dir} not found`, 'yellow');
    }
  });
  
  // Copy individual files
  log('\n📄 Copying files...', 'blue');
  config.files.forEach(file => {
    if (fs.existsSync(file)) {
      copyFile(file, path.join(config.buildDir, file));
      log(`  Copied: ${file}`, 'green');
    } else {
      log(`Warning: File ${file} not found`, 'yellow');
    }
  });
  
  // Update sitemap
  log('\n🗺️  Updating sitemap...', 'blue');
  updateSitemap();
  
  log('\n✅ Build completed successfully!', 'bright');
  log(`📦 Build output: ${path.resolve(config.buildDir)}`, 'cyan');
}

// Run build if called directly
if (require.main === module) {
  try {
    build();
  } catch (error) {
    log(`❌ Build failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

module.exports = { build };
