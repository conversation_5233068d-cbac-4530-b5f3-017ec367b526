// Build configuration for mobile massage website
module.exports = {
  // Directories to copy (relative to project root)
  directories: [
    'scripts',
    'assets', 
    'styles'
  ],

  // Individual files to copy (relative to project root)
  files: [
    'index.html',
    'booking.html', 
    'booking-form.html',
    '.htaccess',
    'sitemap.xml',
    'robots.txt'
  ],

  // Files to exclude from directory copies (glob patterns)
  excludePatterns: [
    '**/*.map',
    '**/*.old',
    '**/*.backup',
    '**/*.tmp',
    '**/*.log',
    '**/node_modules/**',
    '**/.git/**'
  ],

  // Build output directory
  buildDir: 'build',

  // Sitemap configuration
  sitemap: {
    // Files to update lastmod dates for (matches <loc> URLs)
    updateFiles: [
      'https://ocalamobilemassage.com/',
      'https://ocalamobilemassage.com/index.html',
      'https://ocalamobilemassage.com/booking-form.html', 
      'https://ocalamobilemassage.com/booking.html'
    ],
    
    // Date format for lastmod (ISO format YYYY-MM-DD)
    dateFormat: 'YYYY-MM-DD'
  }
};
