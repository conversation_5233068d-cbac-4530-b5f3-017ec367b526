/**
 * Notification Module
 *
 * This module handles the notification window functionality:
 * - Show/hide notification modal
 * - Handle user interactions
 * - Navigate to service area or booking form
 * - Cleanup on page unload
 */

/**
 * Initializes the notification system
 * @returns {Object} - Public methods for controlling notifications
 */
export function initNotification() {
  // Create notification HTML structure
  createNotificationHTML();
  
  // Get notification elements
  const overlay = document.querySelector('.notification-overlay');
  const card = document.querySelector('.notification-card');
  const cancelButton = document.querySelector('.notification-cancel-button');
  const continueButton = document.querySelector('.notification-continue-button');
  const serviceAreaLink = document.querySelector('.service-area-link');
  
  // State management
  let isVisible = false;
  let onContinueCallback = null;
  let onCancelCallback = null;
  
  // Initialize event listeners
  initEventListeners();
  
  /**
   * Creates the notification HTML structure
   */
  function createNotificationHTML() {
    // Check if notification already exists
    if (document.querySelector('.notification-overlay')) {
      return;
    }
    
    const notificationHTML = `
      <div class="notification-overlay">
        <div class="notification-card">
          <div class="notification-holographic"></div>
          <div class="notification-sparkles"></div>
          <div class="notification-content">
            <h2 class="notification-title">Important Information</h2>
            <div class="notification-message">
              <p>Please verify you are in the <span class="service-area-link">service area</span> before booking your appointment.</p>
              <p>A 20% deposit is required to hold your appointment (fully refundable if canceled).</p>
              <p>New clients must complete Health History and Consent forms.</p>
            </div>
            <div class="notification-buttons">
              <button class="notification-button cancel notification-cancel-button">Cancel</button>
              <button class="notification-button continue notification-continue-button">Continue to Booking</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', notificationHTML);
  }
  
  /**
   * Sets up event listeners for notification interactions
   */
  function initEventListeners() {
    // Cancel button
    if (cancelButton) {
      cancelButton.addEventListener('click', handleCancel);
    }
    
    // Continue button
    if (continueButton) {
      continueButton.addEventListener('click', handleContinue);
    }
    
    // Service area link
    if (serviceAreaLink) {
      serviceAreaLink.addEventListener('click', handleServiceAreaClick);
    }
    
    // Close on overlay click (but not on card click)
    if (overlay) {
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          handleCancel();
        }
      });
    }
    
    // Close on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && isVisible) {
        handleCancel();
      }
    });
  }
  
  /**
   * Handles cancel button click
   */
  function handleCancel() {
    hideNotification();
    if (onCancelCallback) {
      onCancelCallback();
    }
  }
  
  /**
   * Handles continue button click
   */
  function handleContinue() {
    hideNotification();
    if (onContinueCallback) {
      onContinueCallback();
    }
  }
  
  /**
   * Handles service area link click
   */
  function handleServiceAreaClick(e) {
    e.preventDefault();
    hideNotification();
    
    // Navigate to service area page
    // This will be handled by the page transitions module
    const serviceAreaButton = document.querySelector('.service-area-button');
    if (serviceAreaButton) {
      serviceAreaButton.click();
    }
  }
  
  /**
   * Shows the notification with optional callbacks
   * @param {Function} onContinue - Callback when user clicks continue
   * @param {Function} onCancel - Callback when user clicks cancel
   */
  function showNotification(onContinue = null, onCancel = null) {
    if (isVisible) return;
    
    // Store callbacks
    onContinueCallback = onContinue;
    onCancelCallback = onCancel;
    
    // Show overlay
    overlay.classList.add('active');
    isVisible = true;
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Add fade-in animation
    card.classList.add('fade-in');
    
    // Focus the continue button for accessibility
    setTimeout(() => {
      if (continueButton) {
        continueButton.focus();
      }
    }, 100);
  }
  
  /**
   * Hides the notification
   */
  function hideNotification() {
    if (!isVisible) return;
    
    // Add fade-out animation
    card.classList.remove('fade-in');
    card.classList.add('fade-out');
    
    // Wait for animation to complete
    setTimeout(() => {
      overlay.classList.remove('active');
      card.classList.remove('fade-out');
      isVisible = false;
      
      // Restore body scroll
      document.body.style.overflow = '';
      
      // Clear callbacks
      onContinueCallback = null;
      onCancelCallback = null;
    }, 300);
  }
  
  /**
   * Cleans up event listeners and removes notification HTML
   */
  function cleanup() {
    // Remove event listeners
    if (cancelButton) {
      cancelButton.removeEventListener('click', handleCancel);
    }
    
    if (continueButton) {
      continueButton.removeEventListener('click', handleContinue);
    }
    
    if (serviceAreaLink) {
      serviceAreaLink.removeEventListener('click', handleServiceAreaClick);
    }
    
    if (overlay) {
      overlay.removeEventListener('click', handleCancel);
    }
    
    // Remove notification HTML
    const existingOverlay = document.querySelector('.notification-overlay');
    if (existingOverlay) {
      existingOverlay.remove();
    }
    
    // Restore body scroll
    document.body.style.overflow = '';
  }
  
  // Return public methods
  return {
    showNotification,
    hideNotification,
    cleanup,
    isVisible: () => isVisible
  };
}

