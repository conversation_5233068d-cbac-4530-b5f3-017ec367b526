/**
 * Booking Page Module
 * 
 * Handles functionality for the booking.html page including:
 * - Iframe URL management
 * - Loading state management
 * - Navigation functions
 * - Error handling for cross-origin iframe issues
 */

/**
 * Navigate back to the previous page (booking form)
 */
function goBack() {
    // Go back to the previous page (booking form)
    window.history.back();
}

/**
 * Hide the loading overlay when iframe loads (CSP compliant)
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        // Use CSS classes instead of inline styles for CSP compliance
        loadingOverlay.classList.add('fade-out');
        setTimeout(() => {
            loadingOverlay.classList.add('hidden');
        }, 500);
    }
}

/**
 * Validate if a URL is from an allowed Setmore domain
 * @param {string} url - The URL to validate
 * @returns {boolean} - True if URL is from allowed domain
 */
function isValidSetmoreUrl(url) {
    try {
        const urlObj = new URL(url);
        const allowedDomains = [
            'mobile-massage.setmore.com',
            'setmore.com',
            'www.setmore.com'
        ];

        // Check if the hostname matches allowed domains or is a subdomain of setmore.com
        return allowedDomains.includes(urlObj.hostname) ||
               urlObj.hostname.endsWith('.setmore.com');
    } catch (error) {
        console.warn('Invalid URL provided:', url);
        return false;
    }
}

/**
 * Set the iframe URL based on the URL parameter with security validation
 */
function setIframeUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const setmoreUrl = urlParams.get('url');
    const iframe = document.getElementById('booking-iframe');

    if (iframe) {
        // Set up iframe load event listener
        iframe.addEventListener('load', hideLoading);

        if (setmoreUrl) {
            const decodedUrl = decodeURIComponent(setmoreUrl);

            // Security: Validate that the URL is from an allowed Setmore domain
            if (isValidSetmoreUrl(decodedUrl)) {
                iframe.src = decodedUrl;
            } else {
                console.warn('Invalid or unauthorized URL provided, using default:', decodedUrl);
                iframe.src = 'https://mobile-massage.setmore.com';
            }
        } else {
            // Fallback to default Setmore URL
            iframe.src = 'https://mobile-massage.setmore.com';
        }
    }
}

/**
 * Initialize the booking page functionality
 */
function initBookingPage() {
    // Set the iframe URL based on URL parameters
    setIframeUrl();

    // Set up back button event listener
    const backButton = document.getElementById('back-button');
    if (backButton) {
        backButton.addEventListener('click', goBack);
    }

    // Handle cases where iframe might not trigger onload
    setTimeout(() => {
        hideLoading();
    }, 5000); // Hide loading after 5 seconds regardless

    // Add proper error handling for iframe loading issues
    const iframe = document.getElementById('booking-iframe');
    if (iframe) {
        iframe.addEventListener('error', function() {
            console.warn('Iframe failed to load, showing fallback message');
            const loadingText = document.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = 'Unable to load booking system. Please try refreshing the page.';
            }
        });
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initBookingPage();
});

// Make functions available globally for onclick handlers
window.goBack = goBack;
window.hideLoading = hideLoading;
