# Refactoring Prompt: Eliminate Inline Styles from booking-form.js

## **Objective:**
Convert all dynamic inline style assignments (`element.style.property = value`) in `booking-form.js` to use CSS classes, enabling a fully secure CSP without `'unsafe-inline'` in any directive.

## **Current Security Issue:**
The file contains **63+ instances** of dynamic inline styling that require `style-src-attr 'unsafe-inline'` in the CSP, creating potential XSS vulnerabilities.

## **Refactoring Strategy - Work in Phases:**

### **Phase 1: CSS Class Creation (First)**
Create comprehensive CSS classes in `styles/components/booking-form-dynamic.css` for all dynamic styles:

```css
/* Animation Control Classes */
.reset-animation { animation: none !important; }
.staggered-animation { animation: slideInUp 0.6s ease forwards; }

/* Display Control Classes */
.force-block { display: block !important; }
.force-hidden { display: none !important; }

/* Notification Classes */
.notification-overlay { /* positioning, background, etc. */ }
.notification-visible { opacity: 1; transform: translateX(0); }
.notification-hidden { opacity: 0; transform: translateX(100%); }

/* Modal Classes */
.modal-overlay { /* fixed positioning, backdrop */ }
.modal-box { /* centered box styling */ }
.modal-title { /* title styling */ }
.modal-content { /* content styling */ }
.modal-button { /* button styling */ }

/* Ready Message Classes */
.ready-message-enhanced { /* padding, background, shadow */ }

/* Animation Order CSS Custom Properties */
.animation-order-0 { --animation-order: 0; }
.animation-order-1 { --animation-order: 1; }
/* ... continue for needed indices */
```

### **Phase 2: JavaScript Refactoring (Work in Small Chunks)**

**Chunk 1: Animation Control (Lines ~900-950)**
- Replace: `option.style.animation = 'none'`
- With: `option.classList.add('reset-animation')`
- Replace: `option.style.setProperty('--animation-order', index)`
- With: `option.classList.add(\`animation-order-\${index}\`)`

**Chunk 2: Display Control (Lines ~900-1320)**
- Replace: `element.style.display = 'block'`
- With: `element.classList.add('force-block')`

**Chunk 3: Notification System (Lines ~1135-1185)**
- Replace entire `notification.style.cssText = \`...\`` block
- With: `notification.className = 'group-size-notification'`
- Replace: `notification.style.opacity/transform` 
- With: `notification.classList.add('notification-visible')`

**Chunk 4: Ready Message Styling (Lines ~1825-1835)**
- Replace: Multiple `readyMessage.style.*` assignments
- With: `readyMessage.classList.add('ready-message-enhanced')`

**Chunk 5: Modal System (Lines ~2180-2270)**
- Replace: All `alertOverlay.style.*` and `alertBox.style.*`
- With: Appropriate CSS classes (`modal-overlay`, `modal-box`, etc.)

### **Phase 3: Testing Strategy**
After each chunk:
1. Test the specific functionality (animations, notifications, modals)
2. Verify no CSP violations in browser console
3. Ensure visual appearance remains identical

### **Phase 4: CSS Custom Properties**
For animation ordering, use CSS custom properties:
```css
.service-option {
  animation-delay: calc(var(--animation-order, 0) * 0.1s);
}
```

### **Phase 5: Final CSP Update**
Once all inline styles are eliminated, update `.htaccess`:
```apache
# Remove style-src-attr 'unsafe-inline' completely
style-src 'self' https://fonts.googleapis.com https://cdnjs.cloudflare.com;
```

## **Specific Code Patterns to Replace:**

### 1. **Animation Reset Pattern:**
```javascript
// BEFORE
option.style.animation = 'none';
option.offsetHeight; // Trigger reflow
option.style.animation = '';

// AFTER
option.classList.add('reset-animation');
option.offsetHeight; // Trigger reflow
option.classList.remove('reset-animation');
```

### 2. **Dynamic Styling Pattern:**
```javascript
// BEFORE
element.style.property = 'value';

// AFTER
element.classList.add('appropriate-css-class');
```

### 3. **CSS Custom Property Pattern:**
```javascript
// BEFORE
element.style.setProperty('--var', value);

// AFTER
element.classList.add(`css-class-${value}`);
```

## **Benefits of This Refactoring:**
- 🔥 **Maximum Security:** No `'unsafe-inline'` needed anywhere
- 🎯 **Better Performance:** CSS classes are faster than inline styles
- 🛠️ **Maintainability:** Centralized styling in CSS files
- 🔍 **Debugging:** Easier to track style changes

## **Work Order:**
1. Create the CSS classes first (complete foundation)
2. Refactor JavaScript in small, testable chunks
3. Test each chunk thoroughly before moving to next
4. Update CSP only after all inline styles are eliminated

This approach ensures you maintain functionality while achieving maximum security through systematic, manageable refactoring.
