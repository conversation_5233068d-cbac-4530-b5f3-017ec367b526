
/**
 * Massage Booking Application
 * A modular application for booking massage services
 */
const MassageBooking = {
    /**
     * Data Module - Contains all application data
     */
    Data: {
        /**
         * Person data - Number of people options
         * @type {Array<Object>}
         */
        persons: [
            {
                id: 'individual',
                name: 'Individual',
                description: 'One person massage session',
                icon: 'fa-solid fa-user',
                count: 1
            },
            {
                id: 'couples',
                name: '<PERSON><PERSON><PERSON>',
                description: 'Back-to-back individual sessions for two',
                icon: 'fa-solid fa-user-group',
                count: 2
            },
            {
                id: 'corporate',
                name: 'Corporate',
                description: 'Group sessions for four or more people',
                icon: 'fa-solid fa-users',
                count: 3
            }
        ],

        /**
         * Service data - Massage styles with their properties
         * @type {Array<Object>}
         */
        services: [
            {
                id: 'swedish',
                name: 'Swedish Massage',
                description: 'Classic, calming, and great for overall relaxation',
                icon: 'fa-solid fa-spa',
                relaxationScore: 9
            },
            {
                id: 'deepTissue',
                name: 'Deep Tissue Massage',
                description: 'Targets deep muscle layers to release tension',
                icon: 'fa-solid fa-dumbbell',
                relaxationScore: 3
            },
            {
                id: 'sports',
                name: 'Sports Massage',
                description: 'Athletic recovery and performance enhancement',
                icon: 'fa-solid fa-person-running',
                relaxationScore: 4
            },
            {
                id: 'neuromuscular',
                name: 'Neuromuscular Therapy',
                description: 'Focused work on trigger points to help relieve pain',
                icon: 'fa-solid fa-brain',
                relaxationScore: 5
            },
            {
                id: 'chair',
                name: 'Chair Massage',
                description: 'Quick relief for neck, back, and shoulders—fully clothed.',
                icon: 'fa-solid fa-chair',
                relaxationScore: 10,
                hasCustomDurations: true
            }
        ],

        /**
         * Duration data - Time options with prices and relaxation scores
         */
        durations: {
            /**
             * Standard durations for most massage types
             * @type {Array<Object>}
             */
            standard: [
                { time: 60,  price: 100, relaxationScore: 3 },
                { time: 90,  price: 135, relaxationScore: 6 },
                { time: 120, price: 170, relaxationScore: 9 }
            ],

            /**
             * Durations specific to couples massage
             * @type {Array<Object>}
             */
            couples: [
                { time: 60, price: 170, relaxationScore: 4 },
                { time: 90, price: 240, relaxationScore: 7 }
            ],

            /**
             * Durations specific to chair massage
             * @type {Array<Object>}
             */
            chair: [
                { time: 60,  price: 100, relaxationScore: 1 },
                { time: 90,  price: 135, relaxationScore: 2 },
                { time: 120, price: 170, relaxationScore: 4 }
            ]
        },

        /**
         * Corporate session configuration
         * @type {Object}
         */
        corporate: {
            sessionDuration: 15, // minutes per session
            sessionPrice: 25,    // price per session
            minSessions: 4,      // minimum sessions required
            maxSessions: 32,     // maximum sessions (8 hours)
            relaxationScore: 2   // base relaxation score for corporate sessions
        },

        /**
         * Get durations for a specific service type and person type
         * @param {string} serviceId - The ID of the service
         * @param {string} personId - The ID of the person type
         * @returns {Array<Object>} Array of duration options
         */
        getDurationsForService: function(serviceId, personId) {
            if (personId === 'couples') {
                return this.durations.couples;
            } else if (serviceId === 'chair') {
                return this.durations.chair;
            } else {
                return this.durations.standard;
            }
        }
    },

    /**
     * State Module - Manages application state
     */
    State: {
        /**
         * Current selected person type
         * @type {Object|null}
         */
        selectedPerson: null,

        /**
         * Current selected service
         * @type {Object|null}
         */
        selectedService: null,

        /**
         * Current selected duration
         * @type {Object|null}
         */
        selectedDuration: null,

        /**
         * Maximum possible relaxation score
         * @type {number}
         */
        maxRelaxationScore: 1,

        /**
         * Whether the person section is collapsed
         * @type {boolean}
         */
        personCollapsed: false,

        /**
         * Whether the service section is collapsed
         * @type {boolean}
         */
        serviceCollapsed: false,

        /**
         * Whether the duration section is collapsed
         * @type {boolean}
         */
        durationCollapsed: false,

        /**
         * Whether the corporate sessions section is collapsed
         * @type {boolean}
         */
        corporateSessionsCollapsed: false,

        /**
         * Whether the summary animation has been played
         * @type {boolean}
         */
        summaryAnimationPlayed: false,

        /**
         * Number of corporate sessions selected
         * @type {number}
         */
        corporateSessions: 4,

        /**
         * Reset the state to initial values
         */
        reset: function() {
            this.selectedPerson = null;
            this.selectedService = null;
            this.selectedDuration = null;
            this.corporateSessions = 4;
            this.maxRelaxationScore = 1;
            this.personCollapsed = false;
            this.corporateSessionsCollapsed = false;
            this.serviceCollapsed = false;
            this.durationCollapsed = false;
            this.summaryAnimationPlayed = false;
        }
    },

    /**
     * DOM Module - Manages DOM references and manipulations
     */
    DOM: {
        /**
         * References to DOM elements
         */
        elements: {
            // Main containers
            personOptionsContainer: null,
            serviceOptionsContainer: null,
            durationOptionsContainer: null,
            bookNowContainer: null,

            // Section containers
            personSection: null,
            unifiedSelectionSection: null,
            serviceSection: null,
            durationSection: null,
            personSummary: null,
            unifiedSelectionSummary: null,
            serviceSummary: null,
            durationSummary: null,

            // Interactive elements
            relaxationLevelText: null,
            bookButton: null,
            editPersonButton: null,
            editUnifiedSelectionButton: null,
            editServiceButton: null,
            editDurationButton: null,
            personNextButton: null,
            serviceNextButton: null,
            unifiedSelectionNextButton: null,



            // Corporate session elements
            corporateSessionsCounter: null,
            corporateSessionsDisplay: null,
            corporateDecreaseSessions: null,
            corporateIncreaseSessions: null,
            corporateSessionsInfo: null,
            corporateTotalPrice: null,
            corporateTotalTime: null
        },

        /**
         * Cache all DOM element references
         */
        cacheElements: function() {
            // Main containers
            this.elements.personOptionsContainer = document.getElementById('person-options');
            this.elements.serviceOptionsContainer = document.getElementById('service-options');
            this.elements.durationOptionsContainer = document.getElementById('duration-options');
            this.elements.bookNowContainer = document.getElementById('book-now-container');

            // Section containers
            this.elements.personSection = document.getElementById('person-section');
            this.elements.unifiedSelectionSection = document.getElementById('unified-selection-section');
            this.elements.serviceSection = document.getElementById('service-section');
            this.elements.durationSection = document.getElementById('duration-section');
            this.elements.personSummary = document.getElementById('person-summary');
            this.elements.unifiedSelectionSummary = document.getElementById('unified-selection-summary');
            this.elements.serviceSummary = document.getElementById('service-summary');
            this.elements.durationSummary = document.getElementById('duration-summary');

            // Interactive elements
            this.elements.relaxationLevelText = document.getElementById('relaxation-level-text');
            this.elements.bookButton = document.getElementById('book-button');
            this.elements.editPersonButton = document.getElementById('edit-person-button');
            this.elements.editUnifiedSelectionButton = document.getElementById('edit-unified-selection-button');
            this.elements.editServiceButton = document.getElementById('edit-service-button');
            this.elements.editDurationButton = document.getElementById('edit-duration-button');
            this.elements.personNextButton = document.getElementById('person-next-button');
            this.elements.serviceNextButton = document.getElementById('service-next-button');
            this.elements.unifiedSelectionNextButton = document.getElementById('unified-selection-next-button');



            // Corporate session elements
            this.elements.corporateSessionsCounter = document.getElementById('corporate-sessions-counter');
            this.elements.corporateSessionsDisplay = document.getElementById('corporate-sessions-display');
            this.elements.corporateDecreaseSessions = document.getElementById('corporate-decrease-sessions');
            this.elements.corporateIncreaseSessions = document.getElementById('corporate-increase-sessions');
            this.elements.corporateSessionsInfo = document.getElementById('corporate-sessions-info');
            this.elements.corporateTotalPrice = document.getElementById('corporate-total-price');
            this.elements.corporateTotalTime = document.getElementById('corporate-total-time');
        },

        /**
         * Toggle element visibility
         * @param {HTMLElement} element - The element to toggle
         * @param {boolean} show - Whether to show or hide the element
         */
        toggleVisibility: function(element, show) {
            if (!element) return; // Guard against null elements

            if (show) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        },

        /**
         * Toggle a class on an element
         * @param {HTMLElement} element - The element to modify
         * @param {string} className - The class to toggle
         * @param {boolean} add - Whether to add or remove the class
         */
        toggleClass: function(element, className, add) {
            if (add) {
                element.classList.add(className);
            } else {
                element.classList.remove(className);
            }
        }
    },

    /**
     * Calculator Module - Handles price and relaxation calculations
     */
    Calculator: {
        /**
         * Calculate price for a duration based on selected service and group size
         * @param {Object} duration - The duration object
         * @returns {number} The calculated price
         */
        calculatePriceForDuration: function(duration) {
            const state = MassageBooking.State;

            if (!state.selectedService) return duration.price;

            let price = duration.price;
            if (state.selectedService.priceAdjustment &&
                state.selectedService.id !== 'couples' &&
                state.selectedService.id !== 'chair') {
                price += state.selectedService.priceAdjustment;
            }

            return price;
        },

        /**
         * Calculate total price for the selected service and duration
         * @returns {number} The total price
         */
        calculateTotalPrice: function() {
            const state = MassageBooking.State;

            // For corporate sessions, use session-based pricing
            if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                return this.calculateCorporatePrice();
            }

            if (!state.selectedService || !state.selectedDuration) return 0;

            return this.calculatePriceForDuration(state.selectedDuration);
        },

        /**
         * Calculate total price for corporate sessions
         * @returns {number} The total price for corporate sessions
         */
        calculateCorporatePrice: function() {
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;

            return state.corporateSessions * corporateConfig.sessionPrice;
        },

        /**
         * Calculate total time for corporate sessions in minutes
         * @returns {number} Total time in minutes
         */
        calculateCorporateTime: function() {
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;

            return state.corporateSessions * corporateConfig.sessionDuration;
        },

        /**
         * Format corporate time as hours and minutes
         * @returns {string} Formatted time string
         */
        formatCorporateTime: function() {
            const totalMinutes = this.calculateCorporateTime();
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;

            if (hours === 0) {
                return `${minutes} minutes`;
            } else if (minutes === 0) {
                return `${hours} hour${hours > 1 ? 's' : ''}`;
            } else {
                return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minutes`;
            }
        },

        /**
         * Calculate relaxation score based on selected service and duration
         * @returns {number} The relaxation score
         */
        calculateRelaxationScore: function() {
            const state = MassageBooking.State;

            if (!state.selectedService) return 0;

            let score = state.selectedService.relaxationScore;

            if (state.selectedDuration) {
                score += state.selectedDuration.relaxationScore;
            }

            return score;
        },

        /**
         * Calculate maximum possible relaxation score for the selected service
         */
        recalculateMaxRelaxationScore: function() {
            const state = MassageBooking.State;

            if (!state.selectedService || !state.selectedPerson) {
                state.maxRelaxationScore = 1;
                return;
            }

            // Get appropriate durations for the selected service and person type
            const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, state.selectedPerson.id);

            // Find the maximum relaxation score from available durations
            const maxDurationScore = Math.max(...durations.map(d => d.relaxationScore));

            // Update the maximum relaxation score
            state.maxRelaxationScore = Math.max(1, state.selectedService.relaxationScore + maxDurationScore);
        }
    },

    /**
     * UI Module - Manages UI state and interactions
     */
    UI: {
        /**
         * Initialize the calculator UI
         */
        init: function() {
            // Populate person options
            this.populatePersonOptions();

            // Populate service options
            this.populateServices();

            // Set up event listeners
            this.setupEventListeners();

            // Update UI state
            this.updateUI();
        },

        /**
         * Populate the corporate sessions interface
         */
        populateCorporateSessions: function() {
            const state = MassageBooking.State;
            const dom = MassageBooking.DOM.elements;

            if (!state.selectedService || !state.selectedPerson || state.selectedPerson.id !== 'corporate') {
                return;
            }

            // Make sure summary is hidden when showing the interface
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);

            // Update the session counter display
            this.updateCorporateSessionsDisplay();

            // Set up event listeners for session counter
            this.setupCorporateSessionsListeners();
        },

        /**
         * Update the corporate sessions display
         */
        updateCorporateSessionsDisplay: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;
            const calculator = MassageBooking.Calculator;

            if (!dom.corporateSessionsDisplay) return;

            // Update sessions display
            dom.corporateSessionsDisplay.textContent = state.corporateSessions;

            // Update total price
            if (dom.corporateTotalPrice) {
                const totalPrice = calculator.calculateCorporatePrice();
                dom.corporateTotalPrice.textContent = `$${totalPrice}`;
            }

            // Update total time
            if (dom.corporateTotalTime) {
                const timeString = calculator.formatCorporateTime();
                dom.corporateTotalTime.textContent = timeString;
            }

            // Update button states
            if (dom.corporateDecreaseSessions) {
                dom.corporateDecreaseSessions.disabled = state.corporateSessions <= corporateConfig.minSessions;
            }

            if (dom.corporateIncreaseSessions) {
                dom.corporateIncreaseSessions.disabled = state.corporateSessions >= corporateConfig.maxSessions;
            }

            // Update info text
            if (dom.corporateSessionsInfo) {
                // Clear existing content
                dom.corporateSessionsInfo.textContent = '';

                // Create icon element
                const icon = document.createElement('i');
                icon.className = 'fas fa-info-circle info-icon';

                // Create text content
                const textContent = document.createTextNode(
                    `15-minute sessions • $${corporateConfig.sessionPrice} each • Minimum ${corporateConfig.minSessions} sessions required`
                );

                // Append elements
                dom.corporateSessionsInfo.appendChild(icon);
                dom.corporateSessionsInfo.appendChild(textContent);
            }

            // Show Next button when minimum sessions are selected and section is not collapsed
            if (dom.unifiedSelectionNextButton) {
                MassageBooking.DOM.toggleVisibility(
                    dom.unifiedSelectionNextButton,
                    state.corporateSessions >= corporateConfig.minSessions && !state.corporateSessionsCollapsed
                );
            }
        },

        /**
         * Set up event listeners for corporate sessions counter
         */
        setupCorporateSessionsListeners: function() {
            const dom = MassageBooking.DOM.elements;

            // Check if listeners are already set up to prevent duplicates
            if (dom.corporateDecreaseSessions && !dom.corporateDecreaseSessions.hasAttribute('data-listener-added')) {
                dom.corporateDecreaseSessions.addEventListener('click', () => {
                    this.adjustCorporateSessions(-4); // Decrease by 4 sessions (1 hour)
                });
                dom.corporateDecreaseSessions.setAttribute('data-listener-added', 'true');
            }

            if (dom.corporateIncreaseSessions && !dom.corporateIncreaseSessions.hasAttribute('data-listener-added')) {
                dom.corporateIncreaseSessions.addEventListener('click', () => {
                    this.adjustCorporateSessions(4); // Increase by 4 sessions (1 hour)
                });
                dom.corporateIncreaseSessions.setAttribute('data-listener-added', 'true');
            }
        },

        /**
         * Adjust the number of corporate sessions
         * @param {number} change - Change amount (positive or negative)
         */
        adjustCorporateSessions: function(change) {
            const state = MassageBooking.State;
            const corporateConfig = MassageBooking.Data.corporate;

            const newSessions = state.corporateSessions + change;

            if (newSessions >= corporateConfig.minSessions && newSessions <= corporateConfig.maxSessions) {
                state.corporateSessions = newSessions;
                this.updateCorporateSessionsDisplay();
                this.updateUI(); // Update the overall UI state
            }
        },

        /**
         * Set up event listeners for UI controls
         */
        setupEventListeners: function() {
            const dom = MassageBooking.DOM.elements;

            // Edit person button
            dom.editPersonButton.addEventListener('click', () => {
                this.expandPersonSection();
                this.hideServiceSection();
                this.hideDurationSection();

                // Scroll to person section
                setTimeout(() => {
                    this.scrollToSection(dom.personSection, 200);
                }, 100);
            });

            // Edit unified selection button (corporate sessions)
            const editUnifiedSelectionButton = document.getElementById('edit-unified-selection-button');

            if (editUnifiedSelectionButton) {
                editUnifiedSelectionButton.addEventListener('click', () => {
                    this.expandCorporateSessionsSection();

                    // Scroll to unified selection section
                    setTimeout(() => {
                        this.scrollToSection(dom.unifiedSelectionSection, 200);
                    }, 100);
                });
            }

            // Edit service button
            if (dom.editServiceButton) {
                dom.editServiceButton.addEventListener('click', () => {
                    this.expandServiceSection();
                    if (MassageBooking.State.durationCollapsed) {
                        this.collapseDurationSection();
                    } else {
                        this.hideDurationSection();
                    }

                    // Scroll to service section
                    setTimeout(() => {
                        this.scrollToSection(dom.serviceSection, 200);
                    }, 100);
                });
            }

            // Edit duration button
            if (dom.editDurationButton) {
                dom.editDurationButton.addEventListener('click', () => {
                    this.expandDurationSection();

                    // Scroll to duration section
                    setTimeout(() => {
                        this.scrollToSection(dom.durationSection, 200);
                    }, 100);
                });
            }

            // Next button for person section
            if (dom.personNextButton) {
                dom.personNextButton.addEventListener('click', () => {
                    this.collapsePersonSection();
                    this.showServiceSection();

                    // Scroll to service section
                    setTimeout(() => {
                        this.scrollToSection(dom.serviceSection, 200);
                    }, 100);
                });
            }



            // Next button for service section
            if (dom.serviceNextButton) {
                dom.serviceNextButton.addEventListener('click', () => {
                    this.collapseServiceSection();

                    // Show unified selection for corporate, otherwise show duration section
                    if (MassageBooking.State.selectedPerson && MassageBooking.State.selectedPerson.id === 'corporate') {
                        this.showUnifiedSelection();

                        // Scroll to unified selection section
                        setTimeout(() => {
                            this.scrollToSection(dom.unifiedSelectionSection, 200);
                        }, 100);
                    } else {
                        this.showDurationSection();

                        // Scroll to duration section
                        setTimeout(() => {
                            this.scrollToSection(dom.durationSection, 200);
                        }, 100);
                    }
                });
            }

            // Next button for unified selection (corporate sessions)
            if (dom.unifiedSelectionNextButton) {
                dom.unifiedSelectionNextButton.addEventListener('click', () => {
                    this.collapseCorporateSessionsSection();

                    // Scroll to booking section
                    setTimeout(() => {
                        this.scrollToBookingSection();
                    }, 300);
                });
            }

            // Book button
            if (dom.bookButton) {
                dom.bookButton.addEventListener('click', () => {
                    this.handleBooking();
                });
            }
        },

        /**
         * Collapse the person section and show summary
         */
        collapsePersonSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            if (state.selectedPerson) {
                // Hide person options and next button
                MassageBooking.DOM.toggleVisibility(dom.personOptionsContainer, false);
                MassageBooking.DOM.toggleVisibility(dom.personNextButton, false);

                // Hide the person header with the "Choose Number of People" text
                MassageBooking.DOM.toggleVisibility(document.getElementById('person-header'), false);

                // Show the person summary in place of the header
                MassageBooking.DOM.toggleVisibility(dom.personSummary, true);
                document.getElementById('person-summary-text').textContent = state.selectedPerson.name;

                // Update state
                state.personCollapsed = true;

                // Force the service section to be visible
                const serviceSection = document.getElementById('service-section');
                serviceSection.classList.remove('hidden-section');
                serviceSection.classList.add('force-block');
                serviceSection.classList.remove('hidden');

                // Reset animation for service options
                document.querySelectorAll('#service-options .service-option').forEach((option, index) => {
                    // Reset the animation by removing and re-adding the element to the DOM
                    option.classList.add('reset-animation');
                    option.offsetHeight; // Trigger reflow
                    option.classList.remove('reset-animation');
                    option.classList.add(`animation-order-${index}`);
                });
            }
        },

        /**
         * Expand the person section to show all options
         */
        expandPersonSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show person options and hide summary
            MassageBooking.DOM.toggleVisibility(dom.personOptionsContainer, true);
            MassageBooking.DOM.toggleVisibility(dom.personSummary, false);

            // Show the person header with the "Choose Number of People" text
            MassageBooking.DOM.toggleVisibility(document.getElementById('person-header'), true);

            // Show next button if a person is selected
            if (state.selectedPerson) {
                MassageBooking.DOM.toggleVisibility(dom.personNextButton, true);
            }

            // Update state
            state.personCollapsed = false;



            // Repopulate service options based on the current person selection
            this.populateServices();

            // Reset animation for person options
            document.querySelectorAll('#person-options .service-option').forEach((option, index) => {
                // Reset the animation by removing and re-adding the element to the DOM
                option.classList.add('reset-animation');
                option.offsetHeight; // Trigger reflow
                option.classList.remove('reset-animation');
                option.classList.add(`animation-order-${index}`);
            });
        },











        /**
         * Show the unified selection section (now corporate sessions)
         */
        showUnifiedSelection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSection, true);

            // If the section is already collapsed, keep it collapsed and don't interfere
            if (state.corporateSessionsCollapsed) {
                // Make sure the summary is shown and interface is hidden
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-interface'), false);
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionNextButton, false);
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), false);
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, true);
                // Don't call populateCorporateSessions when collapsed to avoid interference
            } else {
                // Show the expanded version - make sure summary is hidden initially
                MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), true);
                MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-interface'), true);
                this.populateCorporateSessions();
            }
        },

        /**
         * Hide the unified selection section
         */
        hideUnifiedSelection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSection, false);
        },

        /**
         * Collapse the corporate sessions section and show summary
         */
        collapseCorporateSessionsSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Hide the interface and next button
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-interface'), false);
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionNextButton, false);

            // Hide the header
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), false);

            // Show the summary
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, true);
            const summaryText = `${state.corporateSessions} sessions (${calculator.formatCorporateTime()}) • $${calculator.calculateCorporatePrice()}`;
            document.getElementById('unified-selection-summary-text').textContent = summaryText;

            // Update state
            state.corporateSessionsCollapsed = true;

            // Update UI to trigger booking section visibility check
            this.updateUI();
        },

        /**
         * Expand the corporate sessions section
         */
        expandCorporateSessionsSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show the interface and header
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-interface'), true);
            MassageBooking.DOM.toggleVisibility(document.getElementById('unified-selection-header'), true);

            // Hide the summary
            MassageBooking.DOM.toggleVisibility(dom.unifiedSelectionSummary, false);

            // Update state
            state.corporateSessionsCollapsed = false;

            // Refresh the interface
            this.populateCorporateSessions();
        },



        /**
         * Show the service section
         */
        showServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            // Make sure the service section is visible
            dom.serviceSection.classList.remove('hidden-section');
            dom.serviceSection.classList.add('force-block');
            dom.serviceSection.classList.remove('hidden');
        },

        /**
         * Hide the service section
         */
        hideServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.serviceSection, false);
        },

        /**
         * Collapse the service section and show summary
         */
        collapseServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            if (state.selectedService) {
                // Hide service options and next button
                MassageBooking.DOM.toggleVisibility(dom.serviceOptionsContainer, false);
                MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, false);

                // Hide the service header with the "Choose Your Massage Style" text
                MassageBooking.DOM.toggleVisibility(document.getElementById('service-header'), false);

                // Show the service summary in place of the header
                MassageBooking.DOM.toggleVisibility(dom.serviceSummary, true);
                document.getElementById('service-summary-text').textContent = state.selectedService.name;

                // Update state
                state.serviceCollapsed = true;
            }
        },

        /**
         * Expand the service section to show all options
         */
        expandServiceSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show service options and hide summary
            MassageBooking.DOM.toggleVisibility(dom.serviceOptionsContainer, true);
            MassageBooking.DOM.toggleVisibility(dom.serviceSummary, false);

            // Show the service header with the "Choose Your Massage Style" text
            MassageBooking.DOM.toggleVisibility(document.getElementById('service-header'), true);

            // Show next button if a service is selected
            if (state.selectedService) {
                MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, true);
            }

            // Update state
            state.serviceCollapsed = false;

            // Reset animation for service options
            document.querySelectorAll('.service-option').forEach((option, index) => {
                // Reset the animation by removing and re-adding the element to the DOM
                option.classList.add('reset-animation');
                option.offsetHeight; // Trigger reflow
                option.classList.remove('reset-animation');
                option.classList.add(`animation-order-${index}`);
            });
        },

        /**
         * Show the duration section
         */
        showDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.durationSection, true);
        },

        /**
         * Hide the duration section
         */
        hideDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            MassageBooking.DOM.toggleVisibility(dom.durationSection, false);
        },

        /**
         * Collapse the duration section and show summary
         */
        collapseDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            if (state.selectedDuration) {
                // Hide duration options
                MassageBooking.DOM.toggleVisibility(dom.durationOptionsContainer, false);

                // Show duration summary
                MassageBooking.DOM.toggleVisibility(dom.durationSummary, true);
                dom.durationSummary.textContent = `${state.selectedDuration.time} minutes - $${calculator.calculatePriceForDuration(state.selectedDuration)}`;

                // Show edit button
                MassageBooking.DOM.toggleVisibility(dom.editDurationButton, true);

                // Update state
                state.durationCollapsed = true;
            }
        },

        /**
         * Expand the duration section to show all options
         */
        expandDurationSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;

            // Show duration options
            MassageBooking.DOM.toggleVisibility(dom.durationOptionsContainer, true);

            // Hide duration summary and edit button
            MassageBooking.DOM.toggleVisibility(dom.durationSummary, false);
            MassageBooking.DOM.toggleVisibility(dom.editDurationButton, false);

            // Update state
            state.durationCollapsed = false;
        },

        /**
         * Populate person options in the UI
         */
        populatePersonOptions: function() {
            const dom = MassageBooking.DOM.elements;
            const persons = MassageBooking.Data.persons;

            // Clear existing options
            while (dom.personOptionsContainer.firstChild) {
                dom.personOptionsContainer.removeChild(dom.personOptionsContainer.firstChild);
            }

            // Create person option elements
            persons.forEach((person, index) => {
                const personElement = document.createElement('div');
                personElement.className = 'service-option';

                // Set animation order for staggered appearance
                personElement.classList.add(`animation-order-${index}`);

                // Set accessibility attributes
                personElement.setAttribute('role', 'radio');
                personElement.setAttribute('aria-checked', 'false');
                personElement.setAttribute('tabindex', '0');
                personElement.setAttribute('aria-label', `${person.name}: ${person.description}`);

                // Create DOM structure without innerHTML
                const flexContainer = document.createElement('div');
                flexContainer.className = 'flex items-center gap-3';

                const iconContainer = document.createElement('div');
                iconContainer.className = 'service-selector-icon';
                iconContainer.setAttribute('aria-hidden', 'true');

                const icon = document.createElement('i');
                icon.className = person.icon;
                iconContainer.appendChild(icon);

                const textContainer = document.createElement('div');
                textContainer.className = 'service-text-container';

                const title = document.createElement('h3');
                title.className = 'service-selector-h3 font-semibold';
                title.textContent = person.name;

                const description = document.createElement('p');
                description.className = 'service-selector-p text-sm';
                description.textContent = person.description;

                textContainer.appendChild(title);
                textContainer.appendChild(description);
                flexContainer.appendChild(iconContainer);
                flexContainer.appendChild(textContainer);
                personElement.appendChild(flexContainer);

                // Add click and keyboard event listeners for accessibility
                const selectPerson = () => {
                    // Update selected state visually
                    document.querySelectorAll('#person-options .service-option').forEach(el => {
                        MassageBooking.DOM.toggleClass(el, 'selected', false);
                        el.setAttribute('aria-checked', 'false');
                        el.tabIndex = 0;
                    });
                    MassageBooking.DOM.toggleClass(personElement, 'selected', true);
                    personElement.setAttribute('aria-checked', 'true');
                    personElement.tabIndex = -1; // Remove from tab order once selected

                    // Update application state
                    MassageBooking.State.selectedPerson = person;
                    MassageBooking.State.selectedService = null;
                    MassageBooking.State.selectedDuration = null;



                    // Show the Next button
                    MassageBooking.DOM.toggleVisibility(dom.personNextButton, true);

                    // Repopulate service options based on the new person selection
                    this.populateServices();

                    // Update UI
                    this.updateUI();
                };

                // Mouse click event
                personElement.addEventListener('click', selectPerson);

                // Keyboard event for accessibility
                personElement.addEventListener('keydown', (e) => {
                    // Select on Space or Enter key
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        selectPerson();
                    }
                });

                // Add to container
                dom.personOptionsContainer.appendChild(personElement);
            });
        },

        /**
         * Populate service options in the UI
         */
        populateServices: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const allServices = MassageBooking.Data.services;

            // Filter services based on person selection
            let services = allServices;
            if (state.selectedPerson && state.selectedPerson.id === 'individual') {
                // Remove Chair massage for individual sessions
                services = allServices.filter(service => service.id !== 'chair');
            } else if (state.selectedPerson && state.selectedPerson.id === 'couples') {
                // Remove Chair massage for couples
                services = allServices.filter(service => service.id !== 'chair');
            } else if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                // Show only Chair massage for corporate
                services = allServices.filter(service => service.id === 'chair');
            }

            // Clear existing options
            while (dom.serviceOptionsContainer.firstChild) {
                dom.serviceOptionsContainer.removeChild(dom.serviceOptionsContainer.firstChild);
            }

            // Create service option elements
            services.forEach((service, index) => {
                const serviceElement = document.createElement('div');
                serviceElement.className = 'service-option';

                // Set animation order for staggered appearance
                serviceElement.classList.add(`animation-order-${index}`);

                // Set accessibility attributes
                serviceElement.setAttribute('role', 'radio');
                serviceElement.setAttribute('aria-checked', 'false');
                serviceElement.setAttribute('tabindex', '0');
                serviceElement.setAttribute('aria-label', `${service.name}: ${service.description}`);

                // Create DOM structure without innerHTML
                const flexContainer = document.createElement('div');
                flexContainer.className = 'flex items-center gap-3';

                const iconContainer = document.createElement('div');
                iconContainer.className = 'service-selector-icon';
                iconContainer.setAttribute('aria-hidden', 'true');

                const icon = document.createElement('i');
                icon.className = service.icon;
                iconContainer.appendChild(icon);

                const textContainer = document.createElement('div');
                textContainer.className = 'service-text-container';

                const title = document.createElement('h3');
                title.className = 'service-selector-h3 font-semibold';
                title.textContent = service.name;

                const description = document.createElement('p');
                description.className = 'service-selector-p text-sm';
                description.textContent = service.description;

                textContainer.appendChild(title);
                textContainer.appendChild(description);
                flexContainer.appendChild(iconContainer);
                flexContainer.appendChild(textContainer);
                serviceElement.appendChild(flexContainer);

                // Add click and keyboard event listeners for accessibility
                const selectService = () => {
                    // Update selected state visually
                    document.querySelectorAll('#service-options .service-option').forEach(el => {
                        MassageBooking.DOM.toggleClass(el, 'selected', false);
                        el.setAttribute('aria-checked', 'false');
                        el.tabIndex = 0;
                    });
                    MassageBooking.DOM.toggleClass(serviceElement, 'selected', true);
                    serviceElement.setAttribute('aria-checked', 'true');
                    serviceElement.tabIndex = -1; // Remove from tab order once selected

                    // Update application state
                    MassageBooking.State.selectedService = service;
                    MassageBooking.State.selectedDuration = null;



                    // Show the Next button
                    MassageBooking.DOM.toggleVisibility(dom.serviceNextButton, true);

                    // Update durations and UI
                    this.populateDurations();
                    MassageBooking.Calculator.recalculateMaxRelaxationScore();
                    this.updateUI();
                };

                // Mouse click event
                serviceElement.addEventListener('click', selectService);

                // Keyboard event for accessibility
                serviceElement.addEventListener('keydown', (e) => {
                    // Select on Space or Enter key
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        selectService();
                    }
                });

                // Add to container
                dom.serviceOptionsContainer.appendChild(serviceElement);
            });
        },

        /**
         * Populate duration options based on selected service and person type
         */
        populateDurations: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Clear existing options
            while (dom.durationOptionsContainer.firstChild) {
                dom.durationOptionsContainer.removeChild(dom.durationOptionsContainer.firstChild);
            }

            // Exit if no service or person is selected
            if (!state.selectedService || !state.selectedPerson) return;

            // Get appropriate durations for the selected service and person type
            const durations = MassageBooking.Data.getDurationsForService(state.selectedService.id, state.selectedPerson.id);

            // Create duration option elements
            durations.forEach(duration => {
                const durationElement = document.createElement('div');
                durationElement.className = 'duration-option text-center';

                // Set accessibility attributes
                durationElement.setAttribute('role', 'radio');
                durationElement.setAttribute('aria-checked', 'false');
                durationElement.setAttribute('tabindex', '0');
                durationElement.setAttribute('aria-label', `${duration.time} minutes for $${calculator.calculatePriceForDuration(duration)}`);

                // Create DOM structure without innerHTML
                const timeDiv = document.createElement('div');
                timeDiv.className = 'font-semibold duration-selector-h3';
                timeDiv.textContent = `${duration.time} min`;

                const priceDiv = document.createElement('div');
                priceDiv.className = 'text-sm duration-selector-p';
                priceDiv.textContent = `$${calculator.calculatePriceForDuration(duration)}`;

                durationElement.appendChild(timeDiv);
                durationElement.appendChild(priceDiv);

                // Add click and keyboard event listeners for accessibility
                const selectDuration = () => {
                    // Update selected state visually
                    document.querySelectorAll('.duration-option').forEach(el => {
                        MassageBooking.DOM.toggleClass(el, 'selected', false);
                        el.setAttribute('aria-checked', 'false');
                        el.tabIndex = 0;
                    });
                    MassageBooking.DOM.toggleClass(durationElement, 'selected', true);
                    durationElement.setAttribute('aria-checked', 'true');
                    durationElement.tabIndex = -1; // Remove from tab order once selected

                    // Update application state
                    state.selectedDuration = duration;



                    // Update UI
                    this.updateUI();

                    // For individual/couples, scroll to booking section after duration selection
                    if (state.selectedPerson && state.selectedPerson.id !== 'corporate') {
                        setTimeout(() => {
                            this.scrollToBookingSection();
                        }, 300);
                    }
                };

                // Mouse click event
                durationElement.addEventListener('click', selectDuration);

                // Keyboard event for accessibility
                durationElement.addEventListener('keydown', (e) => {
                    // Select on Space or Enter key
                    if (e.key === ' ' || e.key === 'Enter') {
                        e.preventDefault();
                        selectDuration();
                    }
                });

                // Add to container
                dom.durationOptionsContainer.appendChild(durationElement);
            });
        },

        /**
         * Update UI based on current selections
         */
        updateUI: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Calculate relaxation score for Rive animation
            const relaxationScore = calculator.calculateRelaxationScore();
            const relaxationPercentage = Math.min(100, Math.round((relaxationScore / state.maxRelaxationScore) * 100));

            // Update Rive animation
            updateRelaxationAnimation(relaxationPercentage);

            // Update Person Next button visibility
            if (dom.personNextButton) {
                MassageBooking.DOM.toggleVisibility(
                    dom.personNextButton,
                    state.selectedPerson && !state.personCollapsed
                );
            }

            // Update Service Next button visibility
            if (dom.serviceNextButton) {
                MassageBooking.DOM.toggleVisibility(
                    dom.serviceNextButton,
                    state.selectedService && !state.serviceCollapsed
                );
            }



            // Show appropriate sections based on selection state
            if (state.selectedPerson && state.personCollapsed) {
                this.showServiceSection();
            } else if (!state.selectedPerson) {
                this.hideServiceSection();
            }

            // Show unified selection for corporate after service is selected
            if (state.selectedPerson && state.selectedPerson.id === 'corporate' &&
                state.selectedService && state.serviceCollapsed) {
                this.showUnifiedSelection();

                // Update Next button visibility for corporate sessions (only if not collapsed)
                if (dom.unifiedSelectionNextButton && !state.corporateSessionsCollapsed) {
                    MassageBooking.DOM.toggleVisibility(
                        dom.unifiedSelectionNextButton,
                        state.corporateSessions >= MassageBooking.Data.corporate.minSessions
                    );
                }
            } else {
                this.hideUnifiedSelection();
            }



            // Show duration section based on flow
            if (state.selectedService && state.serviceCollapsed) {
                // For corporate: duration is handled by unified selection, don't show separate duration section
                if (state.selectedPerson && state.selectedPerson.id === 'corporate') {
                    this.hideDurationSection();
                } else {
                    // For individual/couples: show duration directly after service
                    this.showDurationSection();
                }
            } else if (!state.selectedService) {
                this.hideDurationSection();
            }

            // Update Book Now button visibility and animation
            this.updateBookNowSection();
        },

        /**
         * Update the Book Now section based on current selections
         */
        updateBookNowSection: function() {
            const dom = MassageBooking.DOM.elements;
            const state = MassageBooking.State;
            const calculator = MassageBooking.Calculator;

            // Check if all required selections are made
            const isReadyForBooking = state.selectedPerson && state.selectedService &&
                (state.selectedDuration ||
                 (state.selectedPerson.id === 'corporate' && state.corporateSessions >= MassageBooking.Data.corporate.minSessions && state.corporateSessionsCollapsed));

            if (isReadyForBooking) {
                // Show the Book Now button when all selections are made
                MassageBooking.DOM.toggleVisibility(dom.bookNowContainer, true);

                // Calculate total price
                let totalPrice, durationTime;

                if (state.selectedPerson.id === 'corporate') {
                    totalPrice = calculator.calculateCorporatePrice();
                    durationTime = calculator.calculateCorporateTime();
                } else {
                    totalPrice = calculator.calculateTotalPrice();
                    durationTime = state.selectedDuration.time;
                }

                // Update the message with selected person, service, duration, and price
                const readyMessage = dom.bookNowContainer.querySelector('.mb-3.text-xl');

                // Add a subtle background and padding to the message
                readyMessage.classList.add('ready-message-enhanced');

                // Use stacked format with separators for both mobile and desktop
                let personInfo = state.selectedPerson.name;
                let durationInfo;

                if (state.selectedPerson.id === 'corporate') {
                    personInfo = 'Corporate';
                    durationInfo = `${state.corporateSessions} sessions (${calculator.formatCorporateTime()})`;
                } else {
                    durationInfo = `${durationTime} minutes`;
                }

                // Clear existing content
                while (readyMessage.firstChild) {
                    readyMessage.removeChild(readyMessage.firstChild);
                }

                // Create title element
                const titleSpan = document.createElement('span');
                titleSpan.className = 'ready-message-title';
                titleSpan.textContent = 'Your massage experience is ready!';

                // Create details container
                const detailsDiv = document.createElement('div');
                detailsDiv.className = 'ready-message-details';

                // Create detail items
                const personDiv = document.createElement('div');
                personDiv.className = 'ready-message-item ready-message-item-large';
                personDiv.textContent = personInfo;

                const serviceDiv = document.createElement('div');
                serviceDiv.className = 'ready-message-item';
                serviceDiv.textContent = state.selectedService.name;

                const durationDiv = document.createElement('div');
                durationDiv.className = 'ready-message-item';
                durationDiv.textContent = durationInfo;

                const priceDiv = document.createElement('div');
                priceDiv.className = 'ready-message-item';
                priceDiv.textContent = `$${totalPrice}`;

                // Append all elements
                detailsDiv.appendChild(personDiv);
                detailsDiv.appendChild(serviceDiv);
                detailsDiv.appendChild(durationDiv);
                detailsDiv.appendChild(priceDiv);

                readyMessage.appendChild(titleSpan);
                readyMessage.appendChild(detailsDiv);

                // Only play the full animation sequence the first time
                if (!state.summaryAnimationPlayed) {
                    this.animateSummarySection();
                    state.summaryAnimationPlayed = true;
                } else {
                    // For subsequent updates, just show the content without animation
                    this.showSummaryWithoutAnimation();
                }

                dom.bookButton.disabled = false;
            } else {
                // Hide the Book Now button if either selection is missing
                dom.bookNowContainer.classList.remove('visible');

                // Reset all animation classes and flag
                this.resetSummaryAnimation();
                state.summaryAnimationPlayed = false;

                // Use setTimeout to hide the element after the fade-out animation
                setTimeout(() => {
                    // Only hide if we don't meet the booking requirements
                    const shouldHide = !state.selectedService ||
                        (!state.selectedDuration &&
                         !(state.selectedPerson && state.selectedPerson.id === 'corporate' &&
                           state.corporateSessions >= MassageBooking.Data.corporate.minSessions &&
                           state.corporateSessionsCollapsed));

                    if (shouldHide) {
                        MassageBooking.DOM.toggleVisibility(dom.bookNowContainer, false);
                    }
                }, 350); // Match the transition duration

                dom.bookButton.disabled = true;
            }
        },

        /**
         * Animate the summary section with sequential reveal and progressive scrolling
         */
        animateSummarySection: function() {
            const dom = MassageBooking.DOM.elements;

            // First, show the container and make the title visible
            dom.bookNowContainer.classList.add('visible');

            // Step 1: Show the booking ready title container and start scrolling
            setTimeout(() => {
                const bookingTitle = document.getElementById('booking-ready-title');
                bookingTitle.classList.add('title-visible');

                // Start scrolling to bring the summary section into view
                this.scrollToElementSmoothly(dom.bookNowContainer, 'start');

                // Step 2: Animate in the "Your massage experience is ready!" text
                setTimeout(() => {
                    const readyMessageTitle = bookingTitle.querySelector('.ready-message-title');
                    if (readyMessageTitle) {
                        readyMessageTitle.classList.add('animate-in');
                    }

                    // Step 3: Show the details container
                    setTimeout(() => {
                        const detailsContainer = bookingTitle.querySelector('.ready-message-details');
                        if (detailsContainer) {
                            detailsContainer.classList.add('details-visible');

                            // Step 4: Animate in each detail item one by one with progressive scrolling
                            const detailItems = detailsContainer.querySelectorAll('.ready-message-item');
                            detailItems.forEach((item, index) => {
                                setTimeout(() => {
                                    item.classList.add('item-visible');

                                    // Progressive scroll adjustment as items appear
                                    if (index === Math.floor(detailItems.length / 2)) {
                                        // Scroll to center when we're halfway through the details
                                        this.scrollToElementSmoothly(dom.bookNowContainer, 'center');
                                    }
                                }, index * 200); // 200ms delay between each item
                            });

                            // Step 5: Animate in the Book Now button after all details are shown
                            setTimeout(() => {
                                const bookButton = dom.bookButton;
                                if (bookButton) {
                                    bookButton.classList.add('button-visible');

                                    // Final scroll to ensure the button is fully visible
                                    setTimeout(() => {
                                        this.scrollToElementSmoothly(dom.bookNowContainer, 'end');
                                    }, 200);
                                }
                            }, detailItems.length * 200 + 300); // Wait for all items + extra delay
                        }
                    }, 400); // Wait for title animation to complete
                }, 300); // Wait for container to expand
            }, 100); // Small initial delay
        },

        /**
         * Show summary content without animation (for subsequent updates)
         */
        showSummaryWithoutAnimation: function() {
            const dom = MassageBooking.DOM.elements;
            const bookingTitle = document.getElementById('booking-ready-title');

            // Show the container
            dom.bookNowContainer.classList.add('visible');

            // Immediately show all elements without animation
            if (bookingTitle) {
                bookingTitle.classList.add('title-visible');

                const readyMessageTitle = bookingTitle.querySelector('.ready-message-title');
                if (readyMessageTitle) {
                    readyMessageTitle.classList.add('animate-in');
                }

                const detailsContainer = bookingTitle.querySelector('.ready-message-details');
                if (detailsContainer) {
                    detailsContainer.classList.add('details-visible');

                    const detailItems = detailsContainer.querySelectorAll('.ready-message-item');
                    detailItems.forEach(item => {
                        item.classList.add('item-visible');
                    });
                }
            }

            // Show the book button
            if (dom.bookButton) {
                dom.bookButton.classList.add('button-visible');
            }

            // Simple scroll to ensure visibility without the progressive scrolling
            setTimeout(() => {
                this.scrollToElementSmoothly(dom.bookNowContainer, 'center', 100);
            }, 100);
        },

        /**
         * Reset all summary animation classes
         */
        resetSummaryAnimation: function() {
            const bookingTitle = document.getElementById('booking-ready-title');
            const dom = MassageBooking.DOM.elements;

            if (bookingTitle) {
                // Reset title container
                bookingTitle.classList.remove('title-visible');

                // Reset title text
                const readyMessageTitle = bookingTitle.querySelector('.ready-message-title');
                if (readyMessageTitle) {
                    readyMessageTitle.classList.remove('animate-in');
                }

                // Reset details container
                const detailsContainer = bookingTitle.querySelector('.ready-message-details');
                if (detailsContainer) {
                    detailsContainer.classList.remove('details-visible');

                    // Reset all detail items
                    const detailItems = detailsContainer.querySelectorAll('.ready-message-item');
                    detailItems.forEach(item => {
                        item.classList.remove('item-visible');
                    });
                }
            }

            // Reset book button
            if (dom.bookButton) {
                dom.bookButton.classList.remove('button-visible');
            }
        },

        /**
         * Scroll to an element with more control over positioning
         * @param {HTMLElement} targetElement - The element to scroll to
         * @param {string} position - Where to position the element ('start', 'center', 'end')
         * @param {number} delay - Delay before scrolling (default: 0ms)
         */
        scrollToElementSmoothly: function(targetElement, position = 'center', delay = 0) {
            if (targetElement) {
                setTimeout(() => {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: position,
                        inline: 'nearest'
                    });
                }, delay);
            }
        },

        /**
         * Scroll to a specific section smoothly
         * @param {HTMLElement} targetElement - The element to scroll to
         * @param {number} delay - Delay before scrolling (default: 100ms)
         */
        scrollToSection: function(targetElement, delay = 100) {
            if (targetElement) {
                setTimeout(() => {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                    });
                }, delay);
            }
        },

        /**
         * Scroll to the booking section smoothly
         */
        scrollToBookingSection: function() {
            const dom = MassageBooking.DOM.elements;

            if (dom.bookNowContainer) {
                // Use the new scrolling method for consistency
                this.scrollToElementSmoothly(dom.bookNowContainer, 'center', 100);
            }
        },

        /**
         * Get the appropriate Setmore URL based on selections
         * @returns {string} The Setmore URL for the selected service
         */
        getSetmoreUrl: function() {
            const state = MassageBooking.State;

            // Setmore URL mapping based on person type, service, and duration
            const setmoreUrls = {
                individual: {
                    swedish: {
                        60: 'https://mobile-massage.setmore.com/services/bd223969-30ba-4b8b-9fe0-5450fbca5a96',
                        90: 'https://mobile-massage.setmore.com/services/53673f33-db9c-44d1-8bdf-a51707c7cb1d',
                        120: 'https://mobile-massage.setmore.com/services/e5e75139-009d-4552-9d40-cf5c092d7cd0'
                    },
                    deepTissue: {
                        60: 'https://mobile-massage.setmore.com/services/8566a925-95e7-447f-8e0e-a12572701a67',
                        90: 'https://mobile-massage.setmore.com/services/30705ce2-ca70-4066-8d04-ad6472d3718a',
                        120: 'https://mobile-massage.setmore.com/services/8de87e2b-4837-48b1-b6bc-f7708d064bd8'
                    },
                    sports: {
                        60: 'https://mobile-massage.setmore.com/services/18966351-5349-469a-ae5f-e3f03c4fc511',
                        90: 'https://mobile-massage.setmore.com/services/5374edea-f16d-46ca-a44c-e10960596bed',
                        120: 'https://mobile-massage.setmore.com/services/f24785be-2f3e-4170-bb5f-32ad5381b6ef'
                    },
                    neuromuscular: {
                        60: 'https://mobile-massage.setmore.com/services/7d812ebc-a31c-41e6-8e13-74d012adbef3',
                        90: 'https://mobile-massage.setmore.com/services/bfcaefbc-6afb-4fda-bf28-2f4e7faddd4d',
                        120: 'https://mobile-massage.setmore.com/services/6a28e727-be46-44a8-8c0d-5fd58f8ccd99'
                    }
                },
                couples: {
                    swedish: {
                        60: 'https://mobile-massage.setmore.com/services/3553970a-9bfa-4722-a23e-a4244a5f401a',
                        90: 'https://mobile-massage.setmore.com/services/7be76dc4-26f1-4062-843a-fe7096a7990d'
                    },
                    deepTissue: {
                        60: 'https://mobile-massage.setmore.com/services/3e675eb2-bfba-4937-b827-1923f18c58ea',
                        90: 'https://mobile-massage.setmore.com/services/888aa64e-1d27-45a7-bca7-18a8dc15d714'
                    },
                    sports: {
                        60: 'https://mobile-massage.setmore.com/services/b621cdb6-f1d2-45ba-b4a5-536d3663b04b',
                        90: 'https://mobile-massage.setmore.com/services/294c9616-6b80-47b3-9c20-3ca7fe34c045'
                    },
                    neuromuscular: {
                        60: 'https://mobile-massage.setmore.com/services/a00592aa-c0ae-48ae-b182-e82e779fc36a',
                        90: 'https://mobile-massage.setmore.com/services/405cdc6b-772a-450d-8710-c45f16023b9a'
                    }
                },
                corporate: {
                    // Corporate uses chair massage with hour-based sessions
                    chair: {
                        1: 'https://mobile-massage.setmore.com/services/84c60c43-9ce9-4050-8d55-290f76723ff9',
                        2: 'https://mobile-massage.setmore.com/services/eeeb489f-18bd-4734-a1b0-58035509b310',
                        3: 'https://mobile-massage.setmore.com/services/95e6608b-94a8-4695-b26d-c9ca2581bb81',
                        4: 'https://mobile-massage.setmore.com/services/7408e5a4-2879-4165-ad88-37bf8d19a7c8',
                        5: 'https://mobile-massage.setmore.com/services/89e1542c-9dbf-4fab-889e-8b13651650e7',
                        6: 'https://mobile-massage.setmore.com/services/9f6f53d8-d537-4bc4-8018-db06a3145e43',
                        7: 'https://mobile-massage.setmore.com/services/04e00818-ab4f-44ef-9dde-f39e7a18d533',
                        8: 'https://mobile-massage.setmore.com/services/a6edb669-00c5-4673-84ce-d156c33f0d4f'
                    }
                }
            };

            // Determine the correct URL based on selections
            if (state.selectedPerson.id === 'corporate') {
                // Corporate sessions: calculate hours from sessions (4 sessions = 1 hour)
                const hours = Math.ceil(state.corporateSessions / 4);
                return setmoreUrls.corporate.chair[hours] || setmoreUrls.corporate.chair[1];
            } else {
                // Individual or couples: use service type and duration
                const personType = state.selectedPerson.id;
                const serviceType = state.selectedService.id;
                const duration = state.selectedDuration.time;

                return setmoreUrls[personType]?.[serviceType]?.[duration] || 'https://mobile-massage.setmore.com';
            }
        },

        /**
         * Handle the booking button click
         */
        handleBooking: function() {
            const state = MassageBooking.State;

            // Check if we have all required selections
            const hasRequiredSelections = state.selectedService &&
                (state.selectedDuration ||
                 (state.selectedPerson.id === 'corporate' && state.corporateSessions >= MassageBooking.Data.corporate.minSessions && state.corporateSessionsCollapsed));

            if (!hasRequiredSelections) return;

            // Get the appropriate Setmore URL
            const setmoreUrl = this.getSetmoreUrl();

            // Redirect to booking.html with the Setmore URL as a parameter
            window.location.href = `booking.html?url=${encodeURIComponent(setmoreUrl)}`;
        }
    }
};

/**
 * Initialize the entire application
 */
MassageBooking.init = function() {
    // Cache DOM elements
    this.DOM.cacheElements();

    // Initialize UI
    this.UI.init();
};

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize the application
    MassageBooking.init();
});
