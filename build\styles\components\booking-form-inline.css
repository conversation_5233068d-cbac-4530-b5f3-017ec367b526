/**
 * Booking Form Inline Styles
 * 
 * This file contains styles that were previously inline in booking-form.html
 * Moved to external CSS for better Content Security Policy compliance
 */

/* Header Styles */
.booking-form-title {
    font-family: 'Poppins', sans-serif;
    letter-spacing: -0.045em;
    text-shadow: 1px 1.01px 1.01px rgba(0,0,0,0.3);
    color: #fff;
}

.booking-form-subtitle {
    font-family: 'Lato', sans-serif;
    letter-spacing: 1.3px;
    color: rgba(255,255,255,0.9);
}

/* Section Titles */
.section-title {
    color: hsl(184, 70%, 35%);
    font-family: 'Poppins', sans-serif;
}

/* Next Buttons */
.next-button {
    height: 36px;
    border: none;
    background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%));
    font-family: 'Lato', sans-serif;
}

/* Summary Sections */
.summary-section {
    background-color: rgba(79, 209, 197, 0.1);
    color: hsl(184, 70%, 35%);
    font-family: 'Lato', sans-serif;
}

/* Edit Buttons */
.edit-button {
    height: 32px;
    border: solid 1px;
    color: hsl(184, 70%, 40%);
    font-family: 'Lato', sans-serif;
}

/* Duration Edit Button */
.duration-edit-button {
    color: hsl(184, 70%, 40%);
    font-family: 'Lato', sans-serif;
}

/* Booking Ready Title */
.booking-ready-title {
    color: #444;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.4;
    text-shadow: 0 1px 1px rgba(255,255,255,0.8);
}

/* Book Now Button */
.book-now-button {
    border: none;
    background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%));
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase;
    letter-spacing: 2px;
    font-size: 1.25rem;
}

/* Dynamic Content Styles (previously inline) */
.info-icon {
    margin-right: 0.5rem;
    color: hsl(184, 70%, 35%);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.notification-icon {
    font-size: 1.1rem;
}

.duration-selector-h3 {
    font-family: 'Poppins', sans-serif;
}

.duration-selector-p {
    font-family: 'Poppins', sans-serif;
    font-size: 0.85rem;
    margin-top: 0.1rem;
    line-height: 1.2rem;
}

.ready-message-title {
    display: block;
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
}

.ready-message-item-large {
    font-size: 1.3rem;
}

/* Ready Message Styling */
.ready-message-styled {
    padding: 1rem;
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 2px 8px rgba(79, 209, 197, 0.15);
    margin-bottom: 1.5rem;
}

/* Booking Confirmation Modal */
.booking-confirmation-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.booking-confirmation-box {
    background-color: white;
    padding: 2rem;
    border-radius: 1rem;
    max-width: 90%;
    width: 400px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    text-align: center;
    position: relative;
    border: 1px solid rgba(79, 209, 197, 0.3);
}

.booking-confirmation-title {
    color: hsl(184, 70%, 35%);
    margin-bottom: 1rem;
    font-family: 'Poppins', sans-serif;
}

.booking-confirmation-content {
    margin-bottom: 1.5rem;
    font-family: 'Lato', sans-serif;
    color: #555;
    line-height: 1.6;
}

.booking-confirmation-thanks {
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #333;
}

.booking-confirmation-close {
    padding: 0.5rem 1.5rem;
    background: linear-gradient(to right, hsl(184, 70%, 35%), hsl(184, 70%, 45%));
    color: white;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    font-family: 'Lato', sans-serif;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-height: 44px;
}
